"""
Comprehensive API serializers for Fulmark HVAC CRM.
The Best CRM in Europe - Cosmic-level API design.
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from apps.hvac_core.models import (
    Country, City, Industry, ClientType, LeadSource, Stage, ClosingReason,
    Currency, Tag, Company, Contact, Lead, Customer, Request, Deal, 
    Equipment, ServiceOrder, CrmEmail
)
from apps.email_intelligence.models import (
    EmailAccount, EmailMessage, EmailAttachment, TranscriptionResult,
    EmailAnalysisResult
)


# ============================================================================
# BASE SERIALIZERS
# ============================================================================

class BaseModelSerializer(serializers.ModelSerializer):
    """Base serializer with common fields."""
    
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    owner_name = serializers.CharField(source='owner.username', read_only=True)


# ============================================================================
# SUPPORTING MODEL SERIALIZERS
# ============================================================================

class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = ['id', 'name', 'code']


class CitySerializer(serializers.ModelSerializer):
    country_name = serializers.CharField(source='country.name', read_only=True)
    
    class Meta:
        model = City
        fields = ['id', 'name', 'country', 'country_name']


class IndustrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Industry
        fields = ['id', 'name', 'description']


class ClientTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClientType
        fields = ['id', 'name', 'description']


class LeadSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeadSource
        fields = ['id', 'name', 'description']


class StageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stage
        fields = ['id', 'name', 'description', 'order', 'is_active']


class ClosingReasonSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClosingReason
        fields = ['id', 'name', 'is_positive']


class CurrencySerializer(serializers.ModelSerializer):
    class Meta:
        model = Currency
        fields = ['id', 'name', 'code', 'symbol', 'rate_to_pln']


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['id', 'name', 'color']


# ============================================================================
# CORE CRM SERIALIZERS
# ============================================================================

class CompanySerializer(BaseModelSerializer):
    """Company serializer with comprehensive data."""
    
    city_name = serializers.CharField(source='city.name', read_only=True)
    country_name = serializers.CharField(source='country.name', read_only=True)
    type_name = serializers.CharField(source='type.name', read_only=True)
    industry_names = serializers.StringRelatedField(source='industry', many=True, read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    # Statistics
    contacts_count = serializers.SerializerMethodField()
    customers_count = serializers.SerializerMethodField()
    deals_count = serializers.SerializerMethodField()
    equipment_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = [
            'id', 'full_name', 'alternative_names', 'website', 'phone', 'email',
            'street_address', 'city', 'city_name', 'postal_code', 'country', 'country_name',
            'registration_number', 'type', 'type_name', 'industry', 'industry_names',
            'hvac_systems_count', 'annual_maintenance_value', 'ai_health_score',
            'active', 'disqualified', 'tags', 'tag_names',
            'contacts_count', 'customers_count', 'deals_count', 'equipment_count',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]
    
    def get_contacts_count(self, obj):
        return obj.contacts.count()
    
    def get_customers_count(self, obj):
        return obj.customers.count()
    
    def get_deals_count(self, obj):
        return obj.deals.count()
    
    def get_equipment_count(self, obj):
        return obj.equipment.count()


class ContactSerializer(BaseModelSerializer):
    """Contact serializer with relationship data."""
    
    full_name = serializers.CharField(read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    city_name = serializers.CharField(source='city.name', read_only=True)
    country_name = serializers.CharField(source='country.name', read_only=True)
    type_name = serializers.CharField(source='type.name', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    class Meta:
        model = Contact
        fields = [
            'id', 'first_name', 'last_name', 'middle_name', 'full_name',
            'email', 'phone', 'mobile', 'position', 'department',
            'street_address', 'city', 'city_name', 'postal_code', 'country', 'country_name',
            'company', 'company_name', 'type', 'type_name',
            'is_decision_maker', 'technical_contact', 'ai_engagement_score',
            'active', 'disqualified', 'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class LeadSerializer(BaseModelSerializer):
    """Lead serializer with AI scoring."""
    
    full_name = serializers.CharField(read_only=True)
    city_name = serializers.CharField(source='city.name', read_only=True)
    country_name = serializers.CharField(source='country.name', read_only=True)
    type_name = serializers.CharField(source='type.name', read_only=True)
    source_name = serializers.CharField(source='source.name', read_only=True)
    industry_names = serializers.StringRelatedField(source='industry', many=True, read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    class Meta:
        model = Lead
        fields = [
            'id', 'first_name', 'last_name', 'middle_name', 'full_name',
            'email', 'phone', 'mobile', 'company_name', 'company_phone', 'company_email',
            'company_address', 'website', 'street_address', 'city', 'city_name',
            'postal_code', 'country', 'country_name', 'type', 'type_name',
            'industry', 'industry_names', 'source', 'source_name',
            'hvac_interest', 'budget_range', 'project_timeline',
            'ai_score', 'conversion_probability', 'disqualified',
            'contact', 'company', 'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class CustomerSerializer(BaseModelSerializer):
    """Customer serializer with comprehensive profile."""
    
    full_name = serializers.CharField(read_only=True)
    full_address = serializers.CharField(read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    city_name = serializers.CharField(source='city.name', read_only=True)
    country_name = serializers.CharField(source='country.name', read_only=True)
    contact_name = serializers.CharField(source='contact.full_name', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    # Statistics
    deals_count = serializers.SerializerMethodField()
    equipment_count = serializers.SerializerMethodField()
    service_orders_count = serializers.SerializerMethodField()
    requests_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'street_address', 'city', 'city_name', 'postal_code', 'district',
            'country', 'country_name', 'full_address', 'customer_type', 'priority',
            'company', 'company_name', 'contact', 'contact_name',
            'ai_health_score', 'churn_probability', 'lifetime_value',
            'tags', 'tag_names',
            'deals_count', 'equipment_count', 'service_orders_count', 'requests_count',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]
    
    def get_deals_count(self, obj):
        return obj.deals.count()
    
    def get_equipment_count(self, obj):
        return obj.equipment.count()
    
    def get_service_orders_count(self, obj):
        return obj.service_orders.count()
    
    def get_requests_count(self, obj):
        return obj.requests.count()


class RequestSerializer(BaseModelSerializer):
    """Request serializer with customer data."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    contact_name = serializers.CharField(source='contact.full_name', read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    class Meta:
        model = Request
        fields = [
            'id', 'title', 'description', 'priority', 'status',
            'customer', 'customer_name', 'contact', 'contact_name',
            'company', 'company_name', 'hvac_system_type', 'estimated_budget',
            'ai_urgency_score', 'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class DealSerializer(BaseModelSerializer):
    """Deal serializer with 7-stage pipeline."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    contact_name = serializers.CharField(source='contact.full_name', read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    stage_name = serializers.CharField(source='stage.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    closing_reason_name = serializers.CharField(source='closing_reason.name', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    class Meta:
        model = Deal
        fields = [
            'id', 'name', 'description', 'stage', 'stage_name', 'next_step', 'next_step_date',
            'amount', 'currency', 'currency_code', 'probability', 'closing_date', 'win_closing_date',
            'customer', 'customer_name', 'contact', 'contact_name', 'company', 'company_name',
            'lead', 'request', 'closing_reason', 'closing_reason_name',
            'active', 'important', 'ai_win_probability', 'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class EquipmentSerializer(BaseModelSerializer):
    """Equipment serializer with lifecycle tracking."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    installer_name = serializers.CharField(source='installer.username', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    # Calculated fields
    age_years = serializers.ReadOnlyField()
    is_under_warranty = serializers.ReadOnlyField()
    maintenance_overdue = serializers.ReadOnlyField()
    
    class Meta:
        model = Equipment
        fields = [
            'id', 'customer', 'customer_name', 'company', 'company_name',
            'equipment_type', 'brand', 'model', 'serial_number', 'capacity_btu',
            'installation_date', 'installation_location', 'installer', 'installer_name',
            'status', 'warranty_end_date', 'last_maintenance_date', 'next_maintenance_date',
            'purchase_price', 'installation_cost', 'health_score', 'failure_probability',
            'age_years', 'is_under_warranty', 'maintenance_overdue',
            'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class ServiceOrderSerializer(BaseModelSerializer):
    """Service order serializer with pipeline management."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    contact_name = serializers.CharField(source='contact.full_name', read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    equipment_info = serializers.CharField(source='equipment.__str__', read_only=True)
    technician_name = serializers.CharField(source='assigned_technician.username', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    tag_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)
    
    class Meta:
        model = ServiceOrder
        fields = [
            'id', 'order_number', 'title', 'description', 'customer', 'customer_name',
            'contact', 'contact_name', 'company', 'company_name', 'equipment', 'equipment_info',
            'deal', 'request', 'service_type', 'priority', 'status', 'stage_changed_at',
            'scheduled_date', 'estimated_duration', 'assigned_technician', 'technician_name',
            'estimated_cost', 'actual_cost', 'currency', 'currency_code',
            'ai_priority_score', 'completion_probability', 'completed_at',
            'tags', 'tag_names',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


class CrmEmailSerializer(BaseModelSerializer):
    """CRM Email serializer with AI analysis."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    contact_name = serializers.CharField(source='contact.full_name', read_only=True)
    company_name = serializers.CharField(source='company.full_name', read_only=True)
    
    class Meta:
        model = CrmEmail
        fields = [
            'id', 'subject', 'from_email', 'to_email', 'cc_email', 'bcc_email',
            'body_text', 'body_html', 'message_id', 'thread_id', 'sent_date', 'received_date',
            'customer', 'customer_name', 'contact', 'contact_name', 'company', 'company_name',
            'lead', 'deal', 'request', 'service_order',
            'ai_sentiment', 'ai_priority', 'ai_keywords',
            'is_outbound', 'is_processed', 'has_attachments',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'owner', 'owner_name'
        ]


# ============================================================================
# EMAIL INTELLIGENCE SERIALIZERS
# ============================================================================

class EmailAccountSerializer(serializers.ModelSerializer):
    """Email account configuration serializer."""
    
    class Meta:
        model = EmailAccount
        fields = [
            'id', 'name', 'email_address', 'account_type',
            'imap_host', 'imap_port', 'imap_use_ssl', 'username',
            'is_active', 'auto_process', 'last_processed',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'password': {'write_only': True}
        }


class EmailMessageSerializer(serializers.ModelSerializer):
    """Email message serializer with AI analysis."""
    
    account_name = serializers.CharField(source='account.name', read_only=True)
    linked_customer_name = serializers.CharField(source='linked_customer.full_name', read_only=True)
    
    class Meta:
        model = EmailMessage
        fields = [
            'id', 'account', 'account_name', 'message_id', 'sender_email', 'sender_name',
            'recipient_email', 'subject', 'date_received', 'body_text', 'body_html',
            'has_attachments', 'email_type', 'priority', 'sentiment', 'confidence_score',
            'customer_phone', 'customer_address', 'equipment_mentioned', 'service_type_detected',
            'urgency_keywords', 'is_processed', 'processing_error', 'processed_at',
            'linked_customer', 'linked_customer_name', 'auto_linked',
            'created_at', 'updated_at'
        ]


class EmailAttachmentSerializer(serializers.ModelSerializer):
    """Email attachment serializer."""
    
    email_subject = serializers.CharField(source='email.subject', read_only=True)
    
    class Meta:
        model = EmailAttachment
        fields = [
            'id', 'email', 'email_subject', 'filename', 'file_size', 'content_type',
            'attachment_type', 'file_path', 'minio_bucket', 'minio_object_name',
            'is_processed', 'processing_error', 'processed_at',
            'created_at', 'updated_at'
        ]


class TranscriptionResultSerializer(serializers.ModelSerializer):
    """Transcription result serializer."""
    
    attachment_filename = serializers.CharField(source='attachment.filename', read_only=True)
    
    class Meta:
        model = TranscriptionResult
        fields = [
            'id', 'attachment', 'attachment_filename', 'transcribed_text', 'confidence_score',
            'language_detected', 'status', 'processing_time', 'stt_service_used',
            'keywords_extracted', 'entities_detected', 'sentiment_analysis',
            'error_message', 'retry_count',
            'created_at', 'updated_at', 'completed_at'
        ]


class EmailAnalysisResultSerializer(serializers.ModelSerializer):
    """Email analysis result serializer."""
    
    email_subject = serializers.CharField(source='email.subject', read_only=True)
    
    class Meta:
        model = EmailAnalysisResult
        fields = [
            'id', 'email', 'email_subject', 'framework_used', 'model_version',
            'analysis_type', 'analysis_results', 'insights', 'recommendations',
            'processing_time', 'confidence_score', 'created_at'
        ]


# ============================================================================
# USER SERIALIZERS
# ============================================================================

class UserSerializer(serializers.ModelSerializer):
    """User serializer for authentication."""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined']
        read_only_fields = ['id', 'date_joined']
"""
API URLs for Fulmark HVAC CRM.
The Best CRM in Europe - Comprehensive API routing.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularSwaggerView,
    SpectacularRedocView,
)

from .views import (
    # Supporting models
    CountryViewSet, CityViewSet, IndustryViewSet, ClientTypeViewSet,
    LeadSourceViewSet, StageViewSet, ClosingReasonViewSet, CurrencyViewSet,
    TagViewSet,
    
    # Core CRM models
    CompanyViewSet, ContactViewSet, LeadViewSet, CustomerViewSet,
    RequestViewSet, DealViewSet, EquipmentViewSet, ServiceOrderViewSet,
    CrmEmailViewSet,
    
    # Email Intelligence models
    EmailAccountViewSet, EmailMessageViewSet, EmailAttachmentViewSet,
    TranscriptionResultViewSet, EmailAnalysisResultViewSet,
)

# Create router and register viewsets
router = DefaultRouter()

# Supporting models
router.register(r'countries', CountryViewSet)
router.register(r'cities', CityViewSet)
router.register(r'industries', IndustryViewSet)
router.register(r'client-types', ClientTypeViewSet)
router.register(r'lead-sources', LeadSourceViewSet)
router.register(r'stages', StageViewSet)
router.register(r'closing-reasons', ClosingReasonViewSet)
router.register(r'currencies', CurrencyViewSet)
router.register(r'tags', TagViewSet)

# Core CRM models
router.register(r'companies', CompanyViewSet)
router.register(r'contacts', ContactViewSet)
router.register(r'leads', LeadViewSet)
router.register(r'customers', CustomerViewSet)
router.register(r'requests', RequestViewSet)
router.register(r'deals', DealViewSet)
router.register(r'equipment', EquipmentViewSet)
router.register(r'service-orders', ServiceOrderViewSet)
router.register(r'crm-emails', CrmEmailViewSet)

# Email Intelligence models
router.register(r'email-accounts', EmailAccountViewSet)
router.register(r'email-messages', EmailMessageViewSet)
router.register(r'email-attachments', EmailAttachmentViewSet)
router.register(r'transcription-results', TranscriptionResultViewSet)
router.register(r'email-analysis-results', EmailAnalysisResultViewSet)

app_name = 'api'

urlpatterns = [
    # API Documentation
    path('schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='api:schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='api:schema'), name='redoc'),
    
    # JWT Authentication
    path('auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    
    # API endpoints
    path('v1/', include(router.urls)),
]
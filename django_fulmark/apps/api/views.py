"""
Comprehensive API views for Fulmark HVAC CRM.
The Best CRM in Europe - Cosmic-level API implementation.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta

from apps.hvac_core.models import (
    Country, City, Industry, ClientType, LeadSource, Stage, ClosingReason,
    Currency, Tag, Company, Contact, Lead, Customer, Request, Deal, 
    Equipment, ServiceOrder, CrmEmail
)
from apps.email_intelligence.models import (
    EmailAccount, EmailMessage, EmailAttachment, TranscriptionResult,
    EmailAnalysisResult
)
from .serializers import (
    CountrySerializer, CitySerializer, IndustrySerializer, ClientTypeSerializer,
    LeadSourceSerializer, StageSerializer, ClosingReasonSerializer, CurrencySerializer,
    TagSerializer, CompanySerializer, ContactSerializer, LeadSerializer,
    CustomerSerializer, RequestSerializer, DealSerializer, EquipmentSerializer,
    ServiceOrderSerializer, CrmEmailSerializer, EmailAccountSerializer,
    EmailMessageSerializer, EmailAttachmentSerializer, TranscriptionResultSerializer,
    EmailAnalysisResultSerializer
)


# ============================================================================
# BASE VIEWSET
# ============================================================================

class BaseModelViewSet(viewsets.ModelViewSet):
    """Base viewset with common functionality."""
    
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    
    def perform_create(self, serializer):
        """Set created_by and owner on creation."""
        serializer.save(
            created_by=self.request.user,
            owner=self.request.user
        )
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get model statistics."""
        queryset = self.filter_queryset(self.get_queryset())
        total_count = queryset.count()
        
        # Get counts by status if model has status field
        stats = {'total_count': total_count}
        
        if hasattr(self.get_queryset().model, 'status'):
            status_counts = queryset.values('status').annotate(count=Count('id'))
            stats['by_status'] = {item['status']: item['count'] for item in status_counts}
        
        return Response(stats)


# ============================================================================
# SUPPORTING MODEL VIEWSETS
# ============================================================================

class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """Country viewset - read-only."""
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'code']
    ordering = ['name']


class CityViewSet(viewsets.ReadOnlyModelViewSet):
    """City viewset - read-only."""
    queryset = City.objects.select_related('country').all()
    serializer_class = CitySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['country']
    search_fields = ['name', 'country__name']
    ordering = ['name']


class IndustryViewSet(viewsets.ModelViewSet):
    """Industry viewset."""
    queryset = Industry.objects.all()
    serializer_class = IndustrySerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'description']
    ordering = ['name']


class ClientTypeViewSet(viewsets.ModelViewSet):
    """Client type viewset."""
    queryset = ClientType.objects.all()
    serializer_class = ClientTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'description']
    ordering = ['name']


class LeadSourceViewSet(viewsets.ModelViewSet):
    """Lead source viewset."""
    queryset = LeadSource.objects.all()
    serializer_class = LeadSourceSerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'description']
    ordering = ['name']


class StageViewSet(viewsets.ModelViewSet):
    """Stage viewset."""
    queryset = Stage.objects.all()
    serializer_class = StageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering = ['order', 'name']


class ClosingReasonViewSet(viewsets.ModelViewSet):
    """Closing reason viewset."""
    queryset = ClosingReason.objects.all()
    serializer_class = ClosingReasonSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_positive']
    search_fields = ['name']
    ordering = ['name']


class CurrencyViewSet(viewsets.ModelViewSet):
    """Currency viewset."""
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'code']
    ordering = ['name']


class TagViewSet(viewsets.ModelViewSet):
    """Tag viewset."""
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name']
    ordering = ['name']


# ============================================================================
# CORE CRM VIEWSETS
# ============================================================================

class CompanyViewSet(BaseModelViewSet):
    """Company viewset with comprehensive functionality."""
    
    queryset = Company.objects.select_related(
        'city', 'country', 'type'
    ).prefetch_related(
        'industry', 'tags', 'contacts', 'customers', 'deals', 'equipment'
    ).all()
    serializer_class = CompanySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'city', 'country', 'type', 'industry', 'active', 'disqualified'
    ]
    search_fields = [
        'full_name', 'alternative_names', 'email', 'phone', 'website',
        'registration_number'
    ]
    ordering_fields = [
        'full_name', 'created_at', 'ai_health_score', 'annual_maintenance_value'
    ]
    ordering = ['-created_at']
    
    @action(detail=True, methods=['get'])
    def contacts(self, request, pk=None):
        """Get company contacts."""
        company = self.get_object()
        contacts = company.contacts.all()
        serializer = ContactSerializer(contacts, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def customers(self, request, pk=None):
        """Get company customers."""
        company = self.get_object()
        customers = company.customers.all()
        serializer = CustomerSerializer(customers, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def deals(self, request, pk=None):
        """Get company deals."""
        company = self.get_object()
        deals = company.deals.all()
        serializer = DealSerializer(deals, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def equipment(self, request, pk=None):
        """Get company equipment."""
        company = self.get_object()
        equipment = company.equipment.all()
        serializer = EquipmentSerializer(equipment, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def top_by_health_score(self, request):
        """Get companies with highest AI health scores."""
        companies = self.get_queryset().order_by('-ai_health_score')[:10]
        serializer = self.get_serializer(companies, many=True)
        return Response(serializer.data)


class ContactViewSet(BaseModelViewSet):
    """Contact viewset with relationship management."""
    
    queryset = Contact.objects.select_related(
        'company', 'city', 'country', 'type'
    ).prefetch_related('tags').all()
    serializer_class = ContactSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'company', 'city', 'country', 'type', 'is_decision_maker',
        'technical_contact', 'active', 'disqualified'
    ]
    search_fields = [
        'first_name', 'last_name', 'middle_name', 'email', 'phone',
        'mobile', 'position', 'department'
    ]
    ordering_fields = [
        'first_name', 'last_name', 'created_at', 'ai_engagement_score'
    ]
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def decision_makers(self, request):
        """Get decision makers."""
        contacts = self.get_queryset().filter(is_decision_maker=True)
        serializer = self.get_serializer(contacts, many=True)
        return Response(serializer.data)


class LeadViewSet(BaseModelViewSet):
    """Lead viewset with AI scoring."""
    
    queryset = Lead.objects.select_related(
        'city', 'country', 'type', 'source', 'contact', 'company'
    ).prefetch_related('industry', 'tags').all()
    serializer_class = LeadSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'city', 'country', 'type', 'source', 'industry', 'disqualified'
    ]
    search_fields = [
        'first_name', 'last_name', 'middle_name', 'email', 'phone',
        'company_name', 'hvac_interest'
    ]
    ordering_fields = [
        'first_name', 'last_name', 'created_at', 'ai_score', 'conversion_probability'
    ]
    ordering = ['-ai_score']
    
    @action(detail=False, methods=['get'])
    def hot_leads(self, request):
        """Get hot leads with high AI scores."""
        leads = self.get_queryset().filter(
            ai_score__gte=70,
            disqualified=False
        ).order_by('-ai_score')
        serializer = self.get_serializer(leads, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def convert_to_customer(self, request, pk=None):
        """Convert lead to customer."""
        lead = self.get_object()
        
        # Create customer from lead data
        customer_data = {
            'first_name': lead.first_name,
            'last_name': lead.last_name,
            'email': lead.email,
            'phone': lead.phone,
            'street_address': lead.street_address,
            'city': lead.city,
            'postal_code': lead.postal_code,
            'country': lead.country,
            'customer_type': 'residential',  # Default
            'company': lead.company,
            'contact': lead.contact,
            'created_by': request.user,
            'owner': request.user
        }
        
        customer = Customer.objects.create(**customer_data)
        
        # Mark lead as converted (you might want to add a converted field)
        lead.disqualified = True
        lead.save()
        
        serializer = CustomerSerializer(customer, context={'request': request})
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class CustomerViewSet(BaseModelViewSet):
    """Customer viewset with comprehensive profile."""
    
    queryset = Customer.objects.select_related(
        'city', 'country', 'company', 'contact'
    ).prefetch_related('tags').all()
    serializer_class = CustomerSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'city', 'country', 'customer_type', 'priority', 'company', 'district'
    ]
    search_fields = [
        'first_name', 'last_name', 'email', 'phone', 'street_address'
    ]
    ordering_fields = [
        'first_name', 'last_name', 'created_at', 'ai_health_score',
        'churn_probability', 'lifetime_value'
    ]
    ordering = ['-created_at']
    
    @action(detail=True, methods=['get'])
    def profile_360(self, request, pk=None):
        """Get comprehensive 360° customer profile."""
        customer = self.get_object()
        
        # Get related data
        deals = customer.deals.all()
        equipment = customer.equipment.all()
        service_orders = customer.service_orders.all()
        requests = customer.requests.all()
        emails = customer.crm_emails.all()
        
        profile_data = {
            'customer': CustomerSerializer(customer, context={'request': request}).data,
            'deals': DealSerializer(deals, many=True, context={'request': request}).data,
            'equipment': EquipmentSerializer(equipment, many=True, context={'request': request}).data,
            'service_orders': ServiceOrderSerializer(service_orders, many=True, context={'request': request}).data,
            'requests': RequestSerializer(requests, many=True, context={'request': request}).data,
            'emails': CrmEmailSerializer(emails, many=True, context={'request': request}).data,
            'statistics': {
                'total_deals': deals.count(),
                'total_equipment': equipment.count(),
                'total_service_orders': service_orders.count(),
                'total_requests': requests.count(),
                'total_emails': emails.count(),
                'total_deal_value': deals.aggregate(total=Sum('amount'))['total'] or 0,
                'avg_deal_value': deals.aggregate(avg=Avg('amount'))['avg'] or 0,
            }
        }
        
        return Response(profile_data)
    
    @action(detail=False, methods=['get'])
    def at_risk(self, request):
        """Get customers at risk of churning."""
        customers = self.get_queryset().filter(
            churn_probability__gte=0.7
        ).order_by('-churn_probability')
        serializer = self.get_serializer(customers, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def high_value(self, request):
        """Get high-value customers."""
        customers = self.get_queryset().filter(
            lifetime_value__gte=10000
        ).order_by('-lifetime_value')
        serializer = self.get_serializer(customers, many=True)
        return Response(serializer.data)

class RequestViewSet(BaseModelViewSet):
    """Request viewset with priority management."""
    
    queryset = Request.objects.select_related(
        'customer', 'contact', 'company'
    ).prefetch_related('tags').all()
    serializer_class = RequestSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['customer', 'contact', 'company', 'priority', 'status']
    search_fields = ['title', 'description', 'hvac_system_type']
    ordering_fields = ['created_at', 'priority', 'ai_urgency_score']
    ordering = ['-ai_urgency_score', '-created_at']
    
    @action(detail=False, methods=['get'])
    def urgent(self, request):
        """Get urgent requests."""
        requests = self.get_queryset().filter(
            Q(priority='urgent') | Q(ai_urgency_score__gte=80)
        ).order_by('-ai_urgency_score')
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)


class DealViewSet(BaseModelViewSet):
    """Deal viewset with 7-stage pipeline management."""
    
    queryset = Deal.objects.select_related(
        'customer', 'contact', 'company', 'stage', 'currency', 'closing_reason'
    ).prefetch_related('tags').all()
    serializer_class = DealSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'customer', 'contact', 'company', 'stage', 'currency',
        'active', 'important'
    ]
    search_fields = ['name', 'description']
    ordering_fields = [
        'name', 'created_at', 'amount', 'probability', 'closing_date',
        'ai_win_probability'
    ]
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def pipeline_stats(self, request):
        """Get pipeline statistics."""
        queryset = self.filter_queryset(self.get_queryset())
        
        # Stats by stage
        stage_stats = queryset.values('stage__name').annotate(
            count=Count('id'),
            total_value=Sum('amount'),
            avg_probability=Avg('probability')
        ).order_by('stage__order')
        
        # Overall stats
        total_deals = queryset.count()
        total_value = queryset.aggregate(total=Sum('amount'))['total'] or 0
        avg_deal_size = queryset.aggregate(avg=Avg('amount'))['avg'] or 0
        
        # Win rate calculation
        closed_deals = queryset.filter(stage__name__in=['CLOSED_WON', 'CLOSED_LOST'])
        won_deals = queryset.filter(stage__name='CLOSED_WON')
        win_rate = (won_deals.count() / closed_deals.count() * 100) if closed_deals.count() > 0 else 0
        
        return Response({
            'total_deals': total_deals,
            'total_value': total_value,
            'avg_deal_size': avg_deal_size,
            'win_rate': win_rate,
            'by_stage': list(stage_stats)
        })
    
    @action(detail=False, methods=['get'])
    def closing_soon(self, request):
        """Get deals closing in the next 30 days."""
        next_month = timezone.now().date() + timedelta(days=30)
        deals = self.get_queryset().filter(
            closing_date__lte=next_month,
            active=True
        ).order_by('closing_date')
        serializer = self.get_serializer(deals, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def move_stage(self, request, pk=None):
        """Move deal to different stage."""
        deal = self.get_object()
        stage_id = request.data.get('stage_id')
        
        if not stage_id:
            return Response(
                {'error': 'stage_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            stage = Stage.objects.get(id=stage_id)
            deal.stage = stage
            deal.stage_changed_at = timezone.now()
            deal.save()
            
            serializer = self.get_serializer(deal)
            return Response(serializer.data)
        except Stage.DoesNotExist:
            return Response(
                {'error': 'Invalid stage_id'},
                status=status.HTTP_400_BAD_REQUEST
            )


class EquipmentViewSet(BaseModelViewSet):
    """Equipment viewset with lifecycle tracking."""
    
    queryset = Equipment.objects.select_related(
        'customer', 'company', 'installer'
    ).prefetch_related('tags').all()
    serializer_class = EquipmentSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'customer', 'company', 'equipment_type', 'brand', 'status'
    ]
    search_fields = ['model', 'serial_number', 'installation_location']
    ordering_fields = [
        'installation_date', 'health_score', 'failure_probability',
        'next_maintenance_date'
    ]
    ordering = ['-installation_date']
    
    @action(detail=False, methods=['get'])
    def maintenance_due(self, request):
        """Get equipment due for maintenance."""
        today = timezone.now().date()
        equipment = self.get_queryset().filter(
            next_maintenance_date__lte=today,
            status='active'
        ).order_by('next_maintenance_date')
        serializer = self.get_serializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def warranty_expiring(self, request):
        """Get equipment with warranty expiring in 30 days."""
        next_month = timezone.now().date() + timedelta(days=30)
        equipment = self.get_queryset().filter(
            warranty_end_date__lte=next_month,
            warranty_end_date__gte=timezone.now().date(),
            status='active'
        ).order_by('warranty_end_date')
        serializer = self.get_serializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def health_alerts(self, request):
        """Get equipment with low health scores."""
        equipment = self.get_queryset().filter(
            health_score__lt=70,
            status='active'
        ).order_by('health_score')
        serializer = self.get_serializer(equipment, many=True)
        return Response(serializer.data)


class ServiceOrderViewSet(BaseModelViewSet):
    """Service order viewset with pipeline management."""
    
    queryset = ServiceOrder.objects.select_related(
        'customer', 'contact', 'company', 'equipment', 'assigned_technician', 'currency'
    ).prefetch_related('tags').all()
    serializer_class = ServiceOrderSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'customer', 'contact', 'company', 'equipment', 'service_type',
        'priority', 'status', 'assigned_technician'
    ]
    search_fields = ['order_number', 'title', 'description']
    ordering_fields = [
        'created_at', 'scheduled_date', 'priority', 'ai_priority_score'
    ]
    ordering = ['-ai_priority_score', '-created_at']
    
    @action(detail=False, methods=['get'])
    def technician_schedule(self, request):
        """Get technician schedule."""
        technician_id = request.query_params.get('technician_id')
        if not technician_id:
            return Response(
                {'error': 'technician_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        orders = self.get_queryset().filter(
            assigned_technician_id=technician_id,
            status__in=['scheduled', 'in_progress']
        ).order_by('scheduled_date')
        
        serializer = self.get_serializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def emergency_orders(self, request):
        """Get emergency service orders."""
        orders = self.get_queryset().filter(
            Q(priority='emergency') | Q(service_type='emergency')
        ).order_by('-created_at')
        serializer = self.get_serializer(orders, many=True)
        return Response(serializer.data)


class CrmEmailViewSet(BaseModelViewSet):
    """CRM Email viewset with AI analysis."""
    
    queryset = CrmEmail.objects.select_related(
        'customer', 'contact', 'company'
    ).all()
    serializer_class = CrmEmailSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'customer', 'contact', 'company', 'ai_sentiment', 'is_outbound',
        'is_processed', 'has_attachments'
    ]
    search_fields = ['subject', 'from_email', 'to_email', 'body_text']
    ordering_fields = ['sent_date', 'received_date', 'ai_priority']
    ordering = ['-sent_date']
    
    @action(detail=False, methods=['get'])
    def unprocessed(self, request):
        """Get unprocessed emails."""
        emails = self.get_queryset().filter(is_processed=False)
        serializer = self.get_serializer(emails, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def high_priority(self, request):
        """Get high priority emails."""
        emails = self.get_queryset().filter(
            ai_priority__gte=80
        ).order_by('-ai_priority')
        serializer = self.get_serializer(emails, many=True)
        return Response(serializer.data)


# ============================================================================
# EMAIL INTELLIGENCE VIEWSETS
# ============================================================================

class EmailAccountViewSet(viewsets.ModelViewSet):
    """Email account viewset."""
    
    queryset = EmailAccount.objects.all()
    serializer_class = EmailAccountSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['account_type', 'is_active', 'auto_process']
    search_fields = ['name', 'email_address']
    ordering = ['name']


class EmailMessageViewSet(viewsets.ModelViewSet):
    """Email message viewset with AI analysis."""
    
    queryset = EmailMessage.objects.select_related(
        'account', 'linked_customer'
    ).all()
    serializer_class = EmailMessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'account', 'email_type', 'priority', 'sentiment', 'is_processed',
        'linked_customer', 'auto_linked'
    ]
    search_fields = ['subject', 'sender_email', 'body_text']
    ordering_fields = ['date_received', 'confidence_score']
    ordering = ['-date_received']


class EmailAttachmentViewSet(viewsets.ModelViewSet):
    """Email attachment viewset."""
    
    queryset = EmailAttachment.objects.select_related('email').all()
    serializer_class = EmailAttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['email', 'attachment_type', 'is_processed']
    search_fields = ['filename']
    ordering = ['-created_at']


class TranscriptionResultViewSet(viewsets.ModelViewSet):
    """Transcription result viewset."""
    
    queryset = TranscriptionResult.objects.select_related('attachment').all()
    serializer_class = TranscriptionResultSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'language_detected', 'stt_service_used']
    search_fields = ['transcribed_text']
    ordering = ['-created_at']


class EmailAnalysisResultViewSet(viewsets.ModelViewSet):
    """Email analysis result viewset."""
    
    queryset = EmailAnalysisResult.objects.select_related('email').all()
    serializer_class = EmailAnalysisResultSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['email', 'framework_used', 'analysis_type']
    search_fields = ['insights', 'recommendations']
    ordering = ['-created_at']
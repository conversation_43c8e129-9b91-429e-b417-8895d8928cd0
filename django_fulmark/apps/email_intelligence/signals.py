"""
Email Intelligence signals for automatic processing.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
import logging

logger = logging.getLogger(__name__)

# Placeholder for email intelligence signals
# Will be implemented with actual email processing logic

@receiver(post_save, sender=User)
def user_created_handler(sender, instance, created, **kwargs):
    """Handle user creation for email intelligence setup."""
    if created:
        logger.info(f"New user created: {instance.username}")
        # Future: Set up email intelligence for new users

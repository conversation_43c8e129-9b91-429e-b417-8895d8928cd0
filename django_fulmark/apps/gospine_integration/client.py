"""
GoSpine API Client for Fulmark HVAC CRM.
Handles all communication with GoSpine backend services.
"""

import httpx
import asyncio
import logging
from typing import Dict, List, Optional, Any
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
import json
from datetime import timedelta

logger = logging.getLogger(__name__)


class GoSpineAPIError(Exception):
    """Custom exception for GoSpine API errors."""
    pass


class GoSpineClient:
    """
    Comprehensive GoSpine API client with:
    - Connection pooling
    - Retry logic
    - Caching
    - Error handling
    - Async support
    """
    
    def __init__(self):
        self.base_url = settings.GOSPINE_CONFIG['BASE_URL']
        self.api_key = settings.GOSPINE_CONFIG['API_KEY']
        self.timeout = settings.GOSPINE_CONFIG['TIMEOUT']
        self.max_retries = settings.GOSPINE_CONFIG['MAX_RETRIES']
        self.retry_delay = settings.GOSPINE_CONFIG['RETRY_DELAY']
        self.enable_cache = settings.GOSPINE_CONFIG['ENABLE_CACHE']
        self.cache_ttl = settings.GOSPINE_CONFIG['CACHE_TTL']
        
        # HTTP client with connection pooling
        self.client = httpx.Client(
            base_url=self.base_url,
            timeout=self.timeout,
            headers={
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'Fulmark-CRM/1.0'
            }
        )
        
        # Async client
        self.async_client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=self.timeout,
            headers={
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'Fulmark-CRM/1.0'
            }
        )
    
    def _get_cache_key(self, endpoint: str, params: Dict = None) -> str:
        """Generate cache key for request."""
        key_parts = [f"gospine:{endpoint}"]
        if params:
            key_parts.append(json.dumps(params, sort_keys=True))
        return ":".join(key_parts)
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, 
                     params: Dict = None, use_cache: bool = True) -> Dict:
        """Make HTTP request with retry logic and caching."""
        
        # Check cache for GET requests
        if method.upper() == 'GET' and use_cache and self.enable_cache:
            cache_key = self._get_cache_key(endpoint, params)
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.debug(f"Cache hit for {endpoint}")
                return cached_result
        
        # Make request with retry logic
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                response = self.client.request(
                    method=method,
                    url=endpoint,
                    json=data,
                    params=params
                )
                
                if response.status_code >= 400:
                    error_msg = f"GoSpine API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    raise GoSpineAPIError(error_msg)
                
                result = response.json()
                
                # Cache successful GET requests
                if method.upper() == 'GET' and use_cache and self.enable_cache:
                    cache_key = self._get_cache_key(endpoint, params)
                    cache.set(cache_key, result, self.cache_ttl)
                    logger.debug(f"Cached result for {endpoint}")
                
                return result
                
            except (httpx.RequestError, httpx.HTTPStatusError) as e:
                last_exception = e
                if attempt < self.max_retries:
                    logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {self.retry_delay}s: {e}")
                    asyncio.sleep(self.retry_delay)
                else:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
        
        raise GoSpineAPIError(f"Failed to connect to GoSpine API: {last_exception}")
    
    async def _make_async_request(self, method: str, endpoint: str, data: Dict = None, 
                                 params: Dict = None) -> Dict:
        """Make async HTTP request."""
        try:
            response = await self.async_client.request(
                method=method,
                url=endpoint,
                json=data,
                params=params
            )
            
            if response.status_code >= 400:
                error_msg = f"GoSpine API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise GoSpineAPIError(error_msg)
            
            return response.json()
            
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            logger.error(f"Async request failed: {e}")
            raise GoSpineAPIError(f"Failed to connect to GoSpine API: {e}")
    
    # ========================================================================
    # CUSTOMER DATA SYNC
    # ========================================================================
    
    def sync_customer_data(self, customer_data: Dict) -> Dict:
        """Sync customer data to GoSpine."""
        return self._make_request('POST', '/api/v1/customers/sync', data=customer_data)
    
    def get_customer_insights(self, customer_id: int) -> Dict:
        """Get AI insights for customer."""
        return self._make_request('GET', f'/api/v1/customers/{customer_id}/insights')
    
    def update_customer_health_score(self, customer_id: int, score: float) -> Dict:
        """Update customer health score."""
        return self._make_request('PUT', f'/api/v1/customers/{customer_id}/health-score', 
                                data={'score': score})
    
    # ========================================================================
    # EQUIPMENT MANAGEMENT
    # ========================================================================
    
    def sync_equipment_data(self, equipment_data: Dict) -> Dict:
        """Sync equipment data to GoSpine."""
        return self._make_request('POST', '/api/v1/equipment/sync', data=equipment_data)
    
    def get_equipment_health_prediction(self, equipment_id: int) -> Dict:
        """Get equipment health prediction."""
        return self._make_request('GET', f'/api/v1/equipment/{equipment_id}/health-prediction')
    
    def schedule_maintenance(self, equipment_id: int, maintenance_data: Dict) -> Dict:
        """Schedule equipment maintenance."""
        return self._make_request('POST', f'/api/v1/equipment/{equipment_id}/maintenance', 
                                data=maintenance_data)
    
    # ========================================================================
    # EMAIL PROCESSING
    # ========================================================================
    
    def process_email(self, email_data: Dict) -> Dict:
        """Send email for AI processing."""
        return self._make_request('POST', '/api/v1/emails/process', data=email_data)
    
    def get_email_analysis(self, email_id: str) -> Dict:
        """Get email analysis results."""
        return self._make_request('GET', f'/api/v1/emails/{email_id}/analysis')
    
    def transcribe_audio(self, audio_data: Dict) -> Dict:
        """Send audio for transcription."""
        return self._make_request('POST', '/api/v1/transcription/process', data=audio_data)
    
    def get_transcription_result(self, transcription_id: str) -> Dict:
        """Get transcription results."""
        return self._make_request('GET', f'/api/v1/transcription/{transcription_id}/result')
    
    # ========================================================================
    # DEAL PIPELINE
    # ========================================================================
    
    def sync_deal_data(self, deal_data: Dict) -> Dict:
        """Sync deal data to GoSpine."""
        return self._make_request('POST', '/api/v1/deals/sync', data=deal_data)
    
    def get_deal_win_probability(self, deal_id: int) -> Dict:
        """Get AI-calculated deal win probability."""
        return self._make_request('GET', f'/api/v1/deals/{deal_id}/win-probability')
    
    def get_pipeline_analytics(self) -> Dict:
        """Get pipeline analytics."""
        return self._make_request('GET', '/api/v1/deals/pipeline-analytics')
    
    # ========================================================================
    # SERVICE ORDERS
    # ========================================================================
    
    def sync_service_order(self, service_order_data: Dict) -> Dict:
        """Sync service order to GoSpine."""
        return self._make_request('POST', '/api/v1/service-orders/sync', data=service_order_data)
    
    def get_technician_schedule(self, technician_id: int, date_range: Dict) -> Dict:
        """Get technician schedule optimization."""
        return self._make_request('GET', f'/api/v1/technicians/{technician_id}/schedule', 
                                params=date_range)
    
    def optimize_routes(self, service_orders: List[Dict]) -> Dict:
        """Optimize service order routes."""
        return self._make_request('POST', '/api/v1/service-orders/optimize-routes', 
                                data={'service_orders': service_orders})
    
    # ========================================================================
    # AI ANALYTICS
    # ========================================================================
    
    def get_business_insights(self, date_range: Dict = None) -> Dict:
        """Get AI-powered business insights."""
        params = date_range if date_range else {}
        return self._make_request('GET', '/api/v1/analytics/business-insights', params=params)
    
    def get_customer_churn_prediction(self) -> Dict:
        """Get customer churn predictions."""
        return self._make_request('GET', '/api/v1/analytics/churn-prediction')
    
    def get_revenue_forecast(self, months_ahead: int = 6) -> Dict:
        """Get revenue forecast."""
        return self._make_request('GET', '/api/v1/analytics/revenue-forecast', 
                                params={'months_ahead': months_ahead})
    
    # ========================================================================
    # ASYNC METHODS
    # ========================================================================
    
    async def async_process_email(self, email_data: Dict) -> Dict:
        """Async email processing."""
        return await self._make_async_request('POST', '/api/v1/emails/process', data=email_data)
    
    async def async_transcribe_audio(self, audio_data: Dict) -> Dict:
        """Async audio transcription."""
        return await self._make_async_request('POST', '/api/v1/transcription/process', data=audio_data)
    
    async def async_get_insights(self, customer_id: int) -> Dict:
        """Async customer insights."""
        return await self._make_async_request('GET', f'/api/v1/customers/{customer_id}/insights')
    
    # ========================================================================
    # HEALTH CHECK
    # ========================================================================
    
    def health_check(self) -> Dict:
        """Check GoSpine API health."""
        try:
            return self._make_request('GET', '/health', use_cache=False)
        except Exception as e:
            logger.error(f"GoSpine health check failed: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def __del__(self):
        """Cleanup HTTP clients."""
        try:
            self.client.close()
            asyncio.create_task(self.async_client.aclose())
        except Exception:
            pass


# Global client instance
gospine_client = GoSpineClient()
"""
GoSpine Integration Services for Fulmark HVAC CRM.
High-level service layer for GoSpine API integration.
"""

import logging
from typing import Dict, List, Optional
from django.utils import timezone
from django.db import transaction
from celery import shared_task

from apps.hvac_core.models import Customer, Deal, Equipment, ServiceOrder, CrmEmail
from apps.email_intelligence.models import EmailMessage, EmailAttachment, TranscriptionResult
from .client import gospine_client, GoSpineAPIError

logger = logging.getLogger(__name__)


class GoSpineIntegrationService:
    """
    High-level service for GoSpine integration.
    Handles data synchronization and AI processing.
    """
    
    def __init__(self):
        self.client = gospine_client
    
    # ========================================================================
    # CUSTOMER SYNC SERVICES
    # ========================================================================
    
    def sync_customer_to_gospine(self, customer: Customer) -> bool:
        """Sync customer data to GoSpine and update AI scores."""
        try:
            customer_data = {
                'id': customer.id,
                'first_name': customer.first_name,
                'last_name': customer.last_name,
                'email': customer.email,
                'phone': customer.phone,
                'address': customer.full_address,
                'customer_type': customer.customer_type,
                'priority': customer.priority,
                'created_at': customer.created_at.isoformat(),
                'updated_at': customer.updated_at.isoformat(),
            }
            
            # Sync to GoSpine
            result = self.client.sync_customer_data(customer_data)
            
            # Update AI scores if provided
            if 'ai_health_score' in result:
                customer.ai_health_score = result['ai_health_score']
            if 'churn_probability' in result:
                customer.churn_probability = result['churn_probability']
            if 'lifetime_value' in result:
                customer.lifetime_value = result['lifetime_value']
            
            customer.save(update_fields=['ai_health_score', 'churn_probability', 'lifetime_value'])
            
            logger.info(f"Successfully synced customer {customer.id} to GoSpine")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to sync customer {customer.id} to GoSpine: {e}")
            return False
    
    def update_customer_insights(self, customer: Customer) -> Dict:
        """Get and update customer insights from GoSpine."""
        try:
            insights = self.client.get_customer_insights(customer.id)
            
            # Update customer with insights
            if 'health_score' in insights:
                customer.ai_health_score = insights['health_score']
            if 'churn_probability' in insights:
                customer.churn_probability = insights['churn_probability']
            if 'lifetime_value' in insights:
                customer.lifetime_value = insights['lifetime_value']
            
            customer.save(update_fields=['ai_health_score', 'churn_probability', 'lifetime_value'])
            
            logger.info(f"Updated customer {customer.id} insights from GoSpine")
            return insights
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to get customer {customer.id} insights: {e}")
            return {}
    
    # ========================================================================
    # DEAL SYNC SERVICES
    # ========================================================================
    
    def sync_deal_to_gospine(self, deal: Deal) -> bool:
        """Sync deal data to GoSpine and update AI predictions."""
        try:
            deal_data = {
                'id': deal.id,
                'name': deal.name,
                'description': deal.description,
                'amount': float(deal.amount) if deal.amount else 0,
                'stage': deal.stage.name if deal.stage else None,
                'probability': deal.probability,
                'closing_date': deal.closing_date.isoformat() if deal.closing_date else None,
                'customer_id': deal.customer.id if deal.customer else None,
                'created_at': deal.created_at.isoformat(),
                'updated_at': deal.updated_at.isoformat(),
            }
            
            # Sync to GoSpine
            result = self.client.sync_deal_data(deal_data)
            
            # Update AI win probability if provided
            if 'ai_win_probability' in result:
                deal.ai_win_probability = result['ai_win_probability']
                deal.save(update_fields=['ai_win_probability'])
            
            logger.info(f"Successfully synced deal {deal.id} to GoSpine")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to sync deal {deal.id} to GoSpine: {e}")
            return False
    
    def update_deal_win_probability(self, deal: Deal) -> float:
        """Get updated win probability from GoSpine."""
        try:
            result = self.client.get_deal_win_probability(deal.id)
            
            if 'win_probability' in result:
                deal.ai_win_probability = result['win_probability']
                deal.save(update_fields=['ai_win_probability'])
                
                logger.info(f"Updated deal {deal.id} win probability: {result['win_probability']}")
                return result['win_probability']
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to get deal {deal.id} win probability: {e}")
        
        return deal.ai_win_probability or 0.0
    
    # ========================================================================
    # EQUIPMENT SYNC SERVICES
    # ========================================================================
    
    def sync_equipment_to_gospine(self, equipment: Equipment) -> bool:
        """Sync equipment data to GoSpine and update health predictions."""
        try:
            equipment_data = {
                'id': equipment.id,
                'customer_id': equipment.customer.id if equipment.customer else None,
                'equipment_type': equipment.equipment_type,
                'brand': equipment.brand,
                'model': equipment.model,
                'serial_number': equipment.serial_number,
                'installation_date': equipment.installation_date.isoformat() if equipment.installation_date else None,
                'last_maintenance_date': equipment.last_maintenance_date.isoformat() if equipment.last_maintenance_date else None,
                'status': equipment.status,
                'created_at': equipment.created_at.isoformat(),
            }
            
            # Sync to GoSpine
            result = self.client.sync_equipment_data(equipment_data)
            
            # Update health predictions if provided
            if 'health_score' in result:
                equipment.health_score = result['health_score']
            if 'failure_probability' in result:
                equipment.failure_probability = result['failure_probability']
            if 'next_maintenance_date' in result:
                equipment.next_maintenance_date = result['next_maintenance_date']
            
            equipment.save(update_fields=['health_score', 'failure_probability', 'next_maintenance_date'])
            
            logger.info(f"Successfully synced equipment {equipment.id} to GoSpine")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to sync equipment {equipment.id} to GoSpine: {e}")
            return False
    
    def update_equipment_health_prediction(self, equipment: Equipment) -> Dict:
        """Get equipment health prediction from GoSpine."""
        try:
            prediction = self.client.get_equipment_health_prediction(equipment.id)
            
            # Update equipment with predictions
            if 'health_score' in prediction:
                equipment.health_score = prediction['health_score']
            if 'failure_probability' in prediction:
                equipment.failure_probability = prediction['failure_probability']
            if 'recommended_maintenance_date' in prediction:
                equipment.next_maintenance_date = prediction['recommended_maintenance_date']
            
            equipment.save(update_fields=['health_score', 'failure_probability', 'next_maintenance_date'])
            
            logger.info(f"Updated equipment {equipment.id} health prediction")
            return prediction
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to get equipment {equipment.id} health prediction: {e}")
            return {}
    
    # ========================================================================
    # EMAIL PROCESSING SERVICES
    # ========================================================================
    
    def process_email_with_gospine(self, email_message: EmailMessage) -> bool:
        """Process email through GoSpine AI analysis."""
        try:
            email_data = {
                'id': email_message.id,
                'sender_email': email_message.sender_email,
                'subject': email_message.subject,
                'body_text': email_message.body_text,
                'body_html': email_message.body_html,
                'date_received': email_message.date_received.isoformat(),
                'has_attachments': email_message.has_attachments,
            }
            
            # Process through GoSpine
            result = self.client.process_email(email_data)
            
            # Update email with analysis results
            if 'email_type' in result:
                email_message.email_type = result['email_type']
            if 'priority' in result:
                email_message.priority = result['priority']
            if 'sentiment' in result:
                email_message.sentiment = result['sentiment']
            if 'confidence_score' in result:
                email_message.confidence_score = result['confidence_score']
            if 'urgency_keywords' in result:
                email_message.urgency_keywords = result['urgency_keywords']
            
            email_message.is_processed = True
            email_message.processed_at = timezone.now()
            email_message.save()
            
            logger.info(f"Successfully processed email {email_message.id} with GoSpine")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to process email {email_message.id} with GoSpine: {e}")
            email_message.processing_error = str(e)
            email_message.save(update_fields=['processing_error'])
            return False
    
    def transcribe_audio_attachment(self, attachment: EmailAttachment) -> bool:
        """Transcribe audio attachment through GoSpine."""
        try:
            audio_data = {
                'attachment_id': attachment.id,
                'filename': attachment.filename,
                'file_path': attachment.file_path,
                'minio_bucket': attachment.minio_bucket,
                'minio_object_name': attachment.minio_object_name,
            }
            
            # Send for transcription
            result = self.client.transcribe_audio(audio_data)
            
            # Create or update transcription result
            transcription, created = TranscriptionResult.objects.get_or_create(
                attachment=attachment,
                defaults={
                    'status': 'processing',
                    'stt_service_used': result.get('service_used', 'unknown'),
                }
            )
            
            if 'transcription_id' in result:
                # Store transcription ID for later retrieval
                transcription.external_id = result['transcription_id']
                transcription.save()
            
            logger.info(f"Started transcription for attachment {attachment.id}")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to start transcription for attachment {attachment.id}: {e}")
            return False
    
    # ========================================================================
    # SERVICE ORDER SERVICES
    # ========================================================================
    
    def sync_service_order_to_gospine(self, service_order: ServiceOrder) -> bool:
        """Sync service order to GoSpine for optimization."""
        try:
            service_order_data = {
                'id': service_order.id,
                'order_number': service_order.order_number,
                'customer_id': service_order.customer.id if service_order.customer else None,
                'equipment_id': service_order.equipment.id if service_order.equipment else None,
                'service_type': service_order.service_type,
                'priority': service_order.priority,
                'status': service_order.status,
                'scheduled_date': service_order.scheduled_date.isoformat() if service_order.scheduled_date else None,
                'estimated_duration': service_order.estimated_duration,
                'assigned_technician_id': service_order.assigned_technician.id if service_order.assigned_technician else None,
                'created_at': service_order.created_at.isoformat(),
            }
            
            # Sync to GoSpine
            result = self.client.sync_service_order(service_order_data)
            
            # Update AI priority score if provided
            if 'ai_priority_score' in result:
                service_order.ai_priority_score = result['ai_priority_score']
            if 'completion_probability' in result:
                service_order.completion_probability = result['completion_probability']
            
            service_order.save(update_fields=['ai_priority_score', 'completion_probability'])
            
            logger.info(f"Successfully synced service order {service_order.id} to GoSpine")
            return True
            
        except GoSpineAPIError as e:
            logger.error(f"Failed to sync service order {service_order.id} to GoSpine: {e}")
            return False
    
    # ========================================================================
    # ANALYTICS SERVICES
    # ========================================================================
    
    def get_business_insights(self, date_range: Dict = None) -> Dict:
        """Get AI-powered business insights from GoSpine."""
        try:
            insights = self.client.get_business_insights(date_range)
            logger.info("Retrieved business insights from GoSpine")
            return insights
        except GoSpineAPIError as e:
            logger.error(f"Failed to get business insights: {e}")
            return {}
    
    def get_churn_predictions(self) -> List[Dict]:
        """Get customer churn predictions from GoSpine."""
        try:
            predictions = self.client.get_customer_churn_prediction()
            logger.info("Retrieved churn predictions from GoSpine")
            return predictions.get('predictions', [])
        except GoSpineAPIError as e:
            logger.error(f"Failed to get churn predictions: {e}")
            return []
    
    def get_revenue_forecast(self, months_ahead: int = 6) -> Dict:
        """Get revenue forecast from GoSpine."""
        try:
            forecast = self.client.get_revenue_forecast(months_ahead)
            logger.info(f"Retrieved {months_ahead}-month revenue forecast from GoSpine")
            return forecast
        except GoSpineAPIError as e:
            logger.error(f"Failed to get revenue forecast: {e}")
            return {}
    
    # ========================================================================
    # HEALTH CHECK
    # ========================================================================
    
    def health_check(self) -> Dict:
        """Check GoSpine API health."""
        return self.client.health_check()


# Global service instance
gospine_service = GoSpineIntegrationService()


# ============================================================================
# CELERY TASKS FOR ASYNC PROCESSING
# ============================================================================

@shared_task
def sync_customer_to_gospine_task(customer_id: int):
    """Async task to sync customer to GoSpine."""
    try:
        customer = Customer.objects.get(id=customer_id)
        return gospine_service.sync_customer_to_gospine(customer)
    except Customer.DoesNotExist:
        logger.error(f"Customer {customer_id} not found for GoSpine sync")
        return False


@shared_task
def sync_deal_to_gospine_task(deal_id: int):
    """Async task to sync deal to GoSpine."""
    try:
        deal = Deal.objects.get(id=deal_id)
        return gospine_service.sync_deal_to_gospine(deal)
    except Deal.DoesNotExist:
        logger.error(f"Deal {deal_id} not found for GoSpine sync")
        return False


@shared_task
def process_email_with_gospine_task(email_message_id: int):
    """Async task to process email with GoSpine."""
    try:
        email_message = EmailMessage.objects.get(id=email_message_id)
        return gospine_service.process_email_with_gospine(email_message)
    except EmailMessage.DoesNotExist:
        logger.error(f"Email message {email_message_id} not found for GoSpine processing")
        return False


@shared_task
def transcribe_audio_attachment_task(attachment_id: int):
    """Async task to transcribe audio attachment."""
    try:
        attachment = EmailAttachment.objects.get(id=attachment_id)
        return gospine_service.transcribe_audio_attachment(attachment)
    except EmailAttachment.DoesNotExist:
        logger.error(f"Email attachment {attachment_id} not found for transcription")
        return False


@shared_task
def update_customer_insights_task(customer_id: int):
    """Async task to update customer insights."""
    try:
        customer = Customer.objects.get(id=customer_id)
        return gospine_service.update_customer_insights(customer)
    except Customer.DoesNotExist:
        logger.error(f"Customer {customer_id} not found for insights update")
        return {}


@shared_task
def update_equipment_health_task(equipment_id: int):
    """Async task to update equipment health prediction."""
    try:
        equipment = Equipment.objects.get(id=equipment_id)
        return gospine_service.update_equipment_health_prediction(equipment)
    except Equipment.DoesNotExist:
        logger.error(f"Equipment {equipment_id} not found for health update")
        return {}
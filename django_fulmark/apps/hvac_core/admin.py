"""
Admin interface for HVAC Core models.
Comprehensive CRM administration inspired by Django CRM patterns.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    # Supporting Models
    Country, City, Industry, ClientType, LeadSource, Stage, ClosingReason,
    Currency, Tag,
    # Core CRM Models
    Company, Contact, Lead, Customer, Request, Deal, Equipment, ServiceOrder,
    CrmEmail
)


# ============================================================================
# SUPPORTING MODEL ADMINS
# ============================================================================

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    list_display = ['name', 'country']
    list_filter = ['country']
    search_fields = ['name', 'country__name']
    ordering = ['country__name', 'name']


@admin.register(Industry)
class IndustryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']
    ordering = ['name']


@admin.register(ClientType)
class ClientTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']
    ordering = ['name']


@admin.register(LeadSource)
class LeadSourceAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Stage)
class StageAdmin(admin.ModelAdmin):
    list_display = ['name', 'order', 'is_active', 'description']
    list_filter = ['is_active']
    search_fields = ['name']
    ordering = ['order', 'name']


@admin.register(ClosingReason)
class ClosingReasonAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_positive']
    list_filter = ['is_positive']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'symbol', 'rate_to_pln']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ['name', 'color_display']
    search_fields = ['name']
    ordering = ['name']

    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; padding: 2px 8px; border-radius: 3px; color: white;">{}</span>',
            obj.color, obj.name
        )
    color_display.short_description = _('Color Preview')


# ============================================================================
# INLINE ADMINS
# ============================================================================

class ContactInline(admin.TabularInline):
    model = Contact
    extra = 0
    fields = ['first_name', 'last_name', 'email', 'phone', 'position', 'is_decision_maker']
    readonly_fields = ['created_at']


class EquipmentInline(admin.TabularInline):
    model = Equipment
    extra = 0
    fields = ['equipment_type', 'brand', 'model', 'serial_number', 'status', 'health_score']
    readonly_fields = ['created_at', 'health_score']


class ServiceOrderInline(admin.TabularInline):
    model = ServiceOrder
    extra = 0
    fields = ['order_number', 'service_type', 'status', 'priority', 'scheduled_date']
    readonly_fields = ['order_number', 'created_at']


class DealInline(admin.TabularInline):
    model = Deal
    extra = 0
    fields = ['name', 'stage', 'amount', 'probability', 'closing_date']
    readonly_fields = ['created_at']


class CrmEmailInline(admin.TabularInline):
    model = CrmEmail
    extra = 0
    fields = ['subject', 'from_email', 'sent_date', 'ai_sentiment', 'is_processed']
    readonly_fields = ['sent_date', 'ai_sentiment', 'created_at']


# ============================================================================
# CORE CRM MODEL ADMINS
# ============================================================================

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    """Company admin with comprehensive business information."""

    list_display = [
        'full_name', 'city', 'phone', 'email', 'type',
        'hvac_systems_count', 'ai_health_score_display', 'active'
    ]
    list_filter = [
        'type', 'industry', 'city__country', 'active', 'disqualified', 'created_at'
    ]
    search_fields = [
        'full_name', 'alternative_names', 'email', 'phone', 'registration_number'
    ]
    readonly_fields = [
        'id', 'ai_health_score', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['industry', 'tags']
    inlines = [ContactInline, EquipmentInline, ServiceOrderInline, DealInline, CrmEmailInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('full_name', 'alternative_names', 'website', 'phone', 'email')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'postal_code', 'country')
        }),
        ('Business Information', {
            'fields': ('registration_number', 'type', 'industry')
        }),
        ('HVAC Information', {
            'fields': ('hvac_systems_count', 'annual_maintenance_value')
        }),
        ('Status', {
            'fields': ('active', 'disqualified', 'tags')
        }),
        ('AI Insights', {
            'fields': ('ai_health_score',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_health_score_display(self, obj):
        """Display AI health score with color coding."""
        score = obj.ai_health_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_health_score_display.short_description = _('Health Score')
    ai_health_score_display.admin_order_field = 'ai_health_score'


@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    """Contact admin with relationship management."""

    list_display = [
        'full_name', 'company', 'email', 'phone', 'position',
        'is_decision_maker', 'technical_contact', 'ai_engagement_score_display'
    ]
    list_filter = [
        'company', 'type', 'is_decision_maker', 'technical_contact',
        'active', 'disqualified', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'email', 'phone', 'company__full_name'
    ]
    readonly_fields = [
        'id', 'ai_engagement_score', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'email', 'phone', 'mobile')
        }),
        ('Position', {
            'fields': ('company', 'position', 'department', 'type')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'postal_code', 'country')
        }),
        ('HVAC Role', {
            'fields': ('is_decision_maker', 'technical_contact')
        }),
        ('Status', {
            'fields': ('active', 'disqualified', 'tags')
        }),
        ('AI Insights', {
            'fields': ('ai_engagement_score',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_engagement_score_display(self, obj):
        """Display AI engagement score with color coding."""
        score = obj.ai_engagement_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_engagement_score_display.short_description = _('Engagement Score')
    ai_engagement_score_display.admin_order_field = 'ai_engagement_score'


@admin.register(Lead)
class LeadAdmin(admin.ModelAdmin):
    """Lead admin with conversion tracking."""

    list_display = [
        'full_name', 'company_name', 'email', 'phone', 'source',
        'ai_score_display', 'conversion_probability_display', 'disqualified'
    ]
    list_filter = [
        'source', 'type', 'industry', 'disqualified', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'company_name', 'email', 'phone'
    ]
    readonly_fields = [
        'id', 'ai_score', 'conversion_probability', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['industry', 'tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'email', 'phone', 'mobile')
        }),
        ('Company Information', {
            'fields': ('company_name', 'company_phone', 'company_email', 'company_address', 'website')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'postal_code', 'country')
        }),
        ('Business Information', {
            'fields': ('type', 'industry', 'source')
        }),
        ('HVAC Interest', {
            'fields': ('hvac_interest', 'budget_range', 'project_timeline')
        }),
        ('Relations', {
            'fields': ('contact', 'company')
        }),
        ('Status', {
            'fields': ('disqualified', 'tags')
        }),
        ('AI Insights', {
            'fields': ('ai_score', 'conversion_probability'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_score_display(self, obj):
        """Display AI score with color coding."""
        score = obj.ai_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_score_display.short_description = _('AI Score')
    ai_score_display.admin_order_field = 'ai_score'

    def conversion_probability_display(self, obj):
        """Display conversion probability as percentage."""
        probability = obj.conversion_probability * 100
        if probability >= 70:
            color = 'green'
        elif probability >= 40:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, probability
        )
    conversion_probability_display.short_description = _('Conversion %')
    conversion_probability_display.admin_order_field = 'conversion_probability'


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    """Enhanced customer admin with AI insights."""

    list_display = [
        'full_name', 'company', 'email', 'phone', 'city',
        'customer_type', 'priority', 'ai_health_score_display',
        'created_at'
    ]
    list_filter = [
        'customer_type', 'priority', 'city', 'district', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'email', 'phone', 'company__full_name'
    ]
    readonly_fields = [
        'id', 'ai_health_score', 'churn_probability', 'lifetime_value',
        'created_at', 'updated_at'
    ]
    filter_horizontal = ['tags']
    inlines = [EquipmentInline, ServiceOrderInline, DealInline, CrmEmailInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'postal_code', 'district', 'country')
        }),
        ('Business Information', {
            'fields': ('customer_type', 'priority', 'company', 'contact')
        }),
        ('Status', {
            'fields': ('tags',)
        }),
        ('AI Insights', {
            'fields': ('ai_health_score', 'churn_probability', 'lifetime_value'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_health_score_display(self, obj):
        """Display AI health score with color coding."""
        score = obj.ai_health_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_health_score_display.short_description = _('Health Score')
    ai_health_score_display.admin_order_field = 'ai_health_score'


@admin.register(Request)
class RequestAdmin(admin.ModelAdmin):
    """Request admin for customer inquiries."""

    list_display = [
        'title', 'customer', 'priority', 'status', 'ai_urgency_score_display', 'created_at'
    ]
    list_filter = [
        'priority', 'status', 'created_at'
    ]
    search_fields = [
        'title', 'description', 'customer__first_name', 'customer__last_name'
    ]
    readonly_fields = [
        'id', 'ai_urgency_score', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'priority', 'status')
        }),
        ('Relations', {
            'fields': ('customer', 'contact', 'company')
        }),
        ('HVAC Details', {
            'fields': ('hvac_system_type', 'estimated_budget')
        }),
        ('Status', {
            'fields': ('tags',)
        }),
        ('AI Insights', {
            'fields': ('ai_urgency_score',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_urgency_score_display(self, obj):
        """Display AI urgency score with color coding."""
        score = obj.ai_urgency_score
        if score >= 80:
            color = 'red'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'green'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_urgency_score_display.short_description = _('Urgency Score')
    ai_urgency_score_display.admin_order_field = 'ai_urgency_score'


@admin.register(Deal)
class DealAdmin(admin.ModelAdmin):
    """Deal admin with pipeline management."""

    list_display = [
        'name', 'customer', 'stage', 'amount', 'probability',
        'ai_win_probability_display', 'closing_date', 'active'
    ]
    list_filter = [
        'stage', 'active', 'important', 'closing_reason', 'created_at'
    ]
    search_fields = [
        'name', 'description', 'customer__first_name', 'customer__last_name'
    ]
    readonly_fields = [
        'id', 'ai_win_probability', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['tags']
    inlines = [ServiceOrderInline, CrmEmailInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'stage')
        }),
        ('Pipeline Management', {
            'fields': ('next_step', 'next_step_date')
        }),
        ('Financial', {
            'fields': ('amount', 'currency', 'probability')
        }),
        ('Timeline', {
            'fields': ('closing_date', 'win_closing_date')
        }),
        ('Relations', {
            'fields': ('customer', 'contact', 'company', 'lead', 'request')
        }),
        ('Status', {
            'fields': ('active', 'important', 'closing_reason', 'tags')
        }),
        ('AI Insights', {
            'fields': ('ai_win_probability',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_win_probability_display(self, obj):
        """Display AI win probability as percentage."""
        probability = obj.ai_win_probability * 100
        if probability >= 70:
            color = 'green'
        elif probability >= 40:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, probability
        )
    ai_win_probability_display.short_description = _('Win Probability')
    ai_win_probability_display.admin_order_field = 'ai_win_probability'


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    """Equipment admin with lifecycle tracking."""

    list_display = [
        'equipment_display', 'customer', 'company', 'brand', 'model', 'status',
        'installation_date', 'health_score_display', 'warranty_status'
    ]
    list_filter = [
        'equipment_type', 'brand', 'status', 'installation_date', 'company'
    ]
    search_fields = [
        'model', 'serial_number', 'customer__first_name',
        'customer__last_name', 'company__full_name'
    ]
    readonly_fields = [
        'id', 'age_years', 'is_under_warranty', 'maintenance_overdue',
        'health_score', 'failure_probability', 'created_at', 'updated_at'
    ]
    filter_horizontal = ['tags']
    inlines = [ServiceOrderInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('customer', 'company', 'equipment_type', 'brand', 'model', 'serial_number')
        }),
        ('Specifications', {
            'fields': ('capacity_btu',)
        }),
        ('Installation', {
            'fields': ('installation_date', 'installation_location', 'installer')
        }),
        ('Lifecycle', {
            'fields': ('status', 'warranty_end_date', 'last_maintenance_date', 'next_maintenance_date')
        }),
        ('Financial', {
            'fields': ('purchase_price', 'installation_cost')
        }),
        ('Status', {
            'fields': ('tags',)
        }),
        ('AI Insights', {
            'fields': ('health_score', 'failure_probability'),
            'classes': ('collapse',)
        }),
        ('Calculated Fields', {
            'fields': ('age_years', 'is_under_warranty', 'maintenance_overdue'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def equipment_display(self, obj):
        """Display equipment with type icon."""
        icons = {
            'split_ac': '❄️',
            'multi_split': '🏢',
            'vrf': '🏭',
            'chiller': '🧊',
            'heat_pump': '🔥',
            'ventilation': '💨',
            'air_handler': '🌪️',
            'condenser': '🔧',
            'other': '⚙️',
        }
        icon = icons.get(obj.equipment_type, '⚙️')
        return f"{icon} {obj.brand} {obj.model}"
    equipment_display.short_description = _('Equipment')

    def health_score_display(self, obj):
        """Display health score with color coding."""
        score = obj.health_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    health_score_display.short_description = _('Health')
    health_score_display.admin_order_field = 'health_score'

    def warranty_status(self, obj):
        """Display warranty status."""
        if obj.is_under_warranty:
            return format_html('<span style="color: green;">✓ Under Warranty</span>')
        else:
            return format_html('<span style="color: red;">✗ No Warranty</span>')
    warranty_status.short_description = _('Warranty')


@admin.register(ServiceOrder)
class ServiceOrderAdmin(admin.ModelAdmin):
    """Service order admin with pipeline management."""

    list_display = [
        'order_number', 'customer', 'service_type', 'status_display',
        'priority_display', 'assigned_technician', 'scheduled_date', 'created_at'
    ]
    list_filter = [
        'status', 'service_type', 'priority', 'assigned_technician', 'created_at'
    ]
    search_fields = [
        'order_number', 'title', 'customer__first_name',
        'customer__last_name', 'company__full_name'
    ]
    readonly_fields = [
        'id', 'order_number', 'ai_priority_score', 'completion_probability',
        'created_at', 'updated_at', 'stage_changed_at'
    ]
    filter_horizontal = ['tags']
    inlines = [CrmEmailInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('order_number', 'title', 'description', 'service_type', 'priority')
        }),
        ('Relations', {
            'fields': ('customer', 'contact', 'company', 'equipment', 'deal', 'request')
        }),
        ('Pipeline', {
            'fields': ('status', 'stage_changed_at')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'estimated_duration', 'assigned_technician')
        }),
        ('Financial', {
            'fields': ('estimated_cost', 'actual_cost', 'currency')
        }),
        ('Completion', {
            'fields': ('completed_at',)
        }),
        ('Status', {
            'fields': ('tags',)
        }),
        ('AI Insights', {
            'fields': ('ai_priority_score', 'completion_probability'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """Display status with color coding."""
        status_colors = {
            'new_lead': '#007bff',
            'qualified': '#28a745',
            'proposal': '#ffc107',
            'negotiation': '#fd7e14',
            'in_progress': '#6f42c1',
            'closed_won': '#20c997',
            'follow_up': '#6c757d',
        }
        color = status_colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'status'
    
    def priority_display(self, obj):
        """Display priority with color coding."""
        priority_colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'emergency': '#dc3545',
        }
        color = priority_colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_display.short_description = _('Priority')
    priority_display.admin_order_field = 'priority'


@admin.register(CrmEmail)
class CrmEmailAdmin(admin.ModelAdmin):
    """Email admin with CRM integration."""

    list_display = [
        'subject', 'from_email', 'customer', 'sent_date',
        'ai_sentiment_display', 'ai_priority_display', 'is_processed'
    ]
    list_filter = [
        'ai_sentiment', 'is_outbound', 'is_processed', 'has_attachments', 'sent_date'
    ]
    search_fields = [
        'subject', 'from_email', 'to_email', 'body_text', 'customer__first_name', 'customer__last_name'
    ]
    readonly_fields = [
        'id', 'message_id', 'thread_id', 'sent_date', 'received_date',
        'ai_sentiment', 'ai_priority', 'ai_keywords', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('Email Headers', {
            'fields': ('subject', 'from_email', 'to_email', 'cc_email', 'bcc_email')
        }),
        ('Content', {
            'fields': ('body_text', 'body_html')
        }),
        ('Metadata', {
            'fields': ('message_id', 'thread_id', 'sent_date', 'received_date')
        }),
        ('CRM Relations', {
            'fields': ('customer', 'contact', 'company', 'lead', 'deal', 'request', 'service_order')
        }),
        ('Status', {
            'fields': ('is_outbound', 'is_processed', 'has_attachments')
        }),
        ('AI Analysis', {
            'fields': ('ai_sentiment', 'ai_priority', 'ai_keywords'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'owner', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def ai_sentiment_display(self, obj):
        """Display AI sentiment with color coding."""
        sentiment_colors = {
            'positive': 'green',
            'neutral': 'orange',
            'negative': 'red',
        }
        color = sentiment_colors.get(obj.ai_sentiment, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.ai_sentiment.title() if obj.ai_sentiment else 'Unknown'
        )
    ai_sentiment_display.short_description = _('Sentiment')
    ai_sentiment_display.admin_order_field = 'ai_sentiment'

    def ai_priority_display(self, obj):
        """Display AI priority with color coding."""
        priority = obj.ai_priority
        if priority >= 80:
            color = 'red'
        elif priority >= 60:
            color = 'orange'
        else:
            color = 'green'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, priority
        )
    ai_priority_display.short_description = _('AI Priority')
    ai_priority_display.admin_order_field = 'ai_priority'

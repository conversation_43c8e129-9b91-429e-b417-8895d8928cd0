# Generated by Django 4.2.9 on 2025-05-31 06:47

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='City')),
            ],
            options={
                'verbose_name': 'City',
                'verbose_name_plural': 'Cities',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ClientType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=50, unique=True, verbose_name='Client Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Client Type',
                'verbose_name_plural': 'Client Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ClosingReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Closing Reason')),
                ('is_positive', models.BooleanField(default=True, verbose_name='Positive Outcome')),
            ],
            options={
                'verbose_name': 'Closing Reason',
                'verbose_name_plural': 'Closing Reasons',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('full_name', models.CharField(max_length=200, verbose_name='Company Name')),
                ('alternative_names', models.CharField(blank=True, default='', help_text='Separate them with commas.', max_length=200, verbose_name='Alternative Names')),
                ('website', models.URLField(blank=True, default='', verbose_name='Website')),
                ('phone', models.CharField(blank=True, default='', max_length=100, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, default='', max_length=254, verbose_name='Email')),
                ('street_address', models.CharField(blank=True, default='', max_length=200)),
                ('postal_code', models.CharField(blank=True, default='', max_length=10)),
                ('registration_number', models.CharField(blank=True, default='', max_length=30, verbose_name='Registration Number')),
                ('hvac_systems_count', models.PositiveIntegerField(default=0, verbose_name='HVAC Systems Count')),
                ('annual_maintenance_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Annual Maintenance Value')),
                ('ai_health_score', models.FloatField(default=0.0, help_text='AI-calculated company health score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('disqualified', models.BooleanField(default=False, verbose_name='Disqualified')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.city', verbose_name='City')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'db_table': 'hvac_companies',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('middle_name', models.CharField(blank=True, default='', max_length=100, verbose_name='Middle Name')),
                ('email', models.EmailField(blank=True, default='', max_length=254, verbose_name='Email')),
                ('phone', models.CharField(blank=True, default='', max_length=20, verbose_name='Phone')),
                ('mobile', models.CharField(blank=True, default='', max_length=20, verbose_name='Mobile')),
                ('position', models.CharField(blank=True, default='', max_length=100, verbose_name='Position')),
                ('department', models.CharField(blank=True, default='', max_length=100, verbose_name='Department')),
                ('street_address', models.CharField(blank=True, default='', max_length=200)),
                ('postal_code', models.CharField(blank=True, default='', max_length=10)),
                ('is_decision_maker', models.BooleanField(default=False, verbose_name='Decision Maker')),
                ('technical_contact', models.BooleanField(default=False, verbose_name='Technical Contact')),
                ('ai_engagement_score', models.FloatField(default=0.0, help_text='AI-calculated engagement score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('disqualified', models.BooleanField(default=False, verbose_name='Disqualified')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.city', verbose_name='City')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='hvac_core.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Contact',
                'verbose_name_plural': 'Contacts',
                'db_table': 'hvac_contacts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Country')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='Code')),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='Currency Name')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='Currency Code')),
                ('symbol', models.CharField(max_length=5, verbose_name='Symbol')),
                ('rate_to_pln', models.DecimalField(decimal_places=4, default=Decimal('1.0000'), max_digits=10, verbose_name='Rate to PLN')),
            ],
            options={
                'verbose_name': 'Currency',
                'verbose_name_plural': 'Currencies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('phone', models.CharField(max_length=20, verbose_name='Phone')),
                ('street_address', models.CharField(max_length=200)),
                ('postal_code', models.CharField(max_length=10)),
                ('district', models.CharField(blank=True, max_length=50, null=True)),
                ('customer_type', models.CharField(choices=[('residential', 'Residential'), ('commercial', 'Commercial'), ('industrial', 'Industrial')], default='residential', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('ai_health_score', models.FloatField(default=0.0, help_text='AI-calculated customer health score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('churn_probability', models.FloatField(default=0.0, help_text='AI-predicted churn probability (0-1)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('lifetime_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='AI-estimated customer lifetime value', max_digits=10)),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.city', verbose_name='City')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers', to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers', to='hvac_core.contact', verbose_name='Primary Contact')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.country', verbose_name='Country')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'db_table': 'hvac_customers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Deal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250, verbose_name='Deal Name')),
                ('description', models.TextField(blank=True, default='', verbose_name='Description')),
                ('next_step', models.CharField(blank=True, default='', max_length=250, verbose_name='Next Step')),
                ('next_step_date', models.DateField(blank=True, null=True, verbose_name='Next Step Date')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10, null=True, verbose_name='Amount')),
                ('probability', models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Probability (%)')),
                ('closing_date', models.DateField(blank=True, null=True, verbose_name='Expected Closing Date')),
                ('win_closing_date', models.DateTimeField(blank=True, null=True, verbose_name='Actual Win Date')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('important', models.BooleanField(default=False, verbose_name='Important')),
                ('ai_win_probability', models.FloatField(default=0.0, help_text='AI-predicted win probability', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('closing_reason', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.closingreason', verbose_name='Closing Reason')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deals', to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.contact', verbose_name='Primary Contact')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.currency', verbose_name='Currency')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deals', to='hvac_core.customer', verbose_name='Customer')),
            ],
            options={
                'verbose_name': 'Deal',
                'verbose_name_plural': 'Deals',
                'db_table': 'hvac_deals',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('equipment_type', models.CharField(choices=[('split_ac', 'Split Air Conditioner'), ('multi_split', 'Multi-Split System'), ('vrf', 'VRF System'), ('chiller', 'Chiller'), ('heat_pump', 'Heat Pump'), ('ventilation', 'Ventilation System'), ('air_handler', 'Air Handler'), ('condenser', 'Condenser Unit'), ('other', 'Other')], max_length=20, verbose_name='Equipment Type')),
                ('brand', models.CharField(choices=[('lg', 'LG'), ('daikin', 'Daikin'), ('mitsubishi', 'Mitsubishi'), ('samsung', 'Samsung'), ('panasonic', 'Panasonic'), ('carrier', 'Carrier'), ('trane', 'Trane'), ('york', 'York'), ('other', 'Other')], max_length=20, verbose_name='Brand')),
                ('model', models.CharField(max_length=100, verbose_name='Model')),
                ('serial_number', models.CharField(max_length=100, unique=True, verbose_name='Serial Number')),
                ('capacity_btu', models.IntegerField(help_text='Capacity in BTU/h', verbose_name='Capacity (BTU/h)')),
                ('installation_date', models.DateField(verbose_name='Installation Date')),
                ('installation_location', models.CharField(max_length=200, verbose_name='Installation Location')),
                ('status', models.CharField(choices=[('active', 'Active'), ('maintenance', 'Under Maintenance'), ('warranty', 'Under Warranty'), ('retired', 'Retired'), ('faulty', 'Faulty'), ('pending_install', 'Pending Installation')], default='active', max_length=20, verbose_name='Status')),
                ('warranty_end_date', models.DateField(blank=True, null=True, verbose_name='Warranty End Date')),
                ('last_maintenance_date', models.DateField(blank=True, null=True, verbose_name='Last Maintenance Date')),
                ('next_maintenance_date', models.DateField(blank=True, null=True, verbose_name='Next Maintenance Date')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Purchase Price')),
                ('installation_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Installation Cost')),
                ('health_score', models.FloatField(default=100.0, help_text='AI-calculated equipment health score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)], verbose_name='Health Score')),
                ('failure_probability', models.FloatField(default=0.0, help_text='AI-predicted failure probability', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Failure Probability')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='equipment', to='hvac_core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment', to='hvac_core.customer', verbose_name='Customer')),
                ('installer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Installer')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Equipment',
                'verbose_name_plural': 'Equipment',
                'db_table': 'hvac_equipment',
                'ordering': ['-installation_date'],
            },
        ),
        migrations.CreateModel(
            name='Industry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Industry')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Industry',
                'verbose_name_plural': 'Industries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LeadSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Lead Source')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Lead Source',
                'verbose_name_plural': 'Lead Sources',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Request',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('status', models.CharField(choices=[('new', 'New'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='new', max_length=20)),
                ('hvac_system_type', models.CharField(blank=True, default='', max_length=100)),
                ('estimated_budget', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Estimated Budget')),
                ('ai_urgency_score', models.FloatField(default=0.0, help_text='AI-calculated urgency score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.contact', verbose_name='Contact Person')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='hvac_core.customer', verbose_name='Customer')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Request',
                'verbose_name_plural': 'Requests',
                'db_table': 'hvac_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Stage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Stage')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Stage',
                'verbose_name_plural': 'Stages',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Tag')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='Color')),
            ],
            options={
                'verbose_name': 'Tag',
                'verbose_name_plural': 'Tags',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='Order Number')),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('service_type', models.CharField(choices=[('installation', 'Installation'), ('maintenance', 'Maintenance'), ('repair', 'Repair'), ('inspection', 'Inspection'), ('consultation', 'Consultation'), ('warranty', 'Warranty Service'), ('emergency', 'Emergency Service'), ('upgrade', 'System Upgrade')], max_length=20, verbose_name='Service Type')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('emergency', 'Emergency')], default='medium', max_length=10, verbose_name='Priority')),
                ('status', models.CharField(choices=[('new_lead', 'New Lead'), ('qualified', 'Qualified'), ('proposal', 'Proposal'), ('negotiation', 'Negotiation'), ('in_progress', 'In Progress'), ('closed_won', 'Closed Won'), ('follow_up', 'Follow Up'), ('cancelled', 'Cancelled')], default='new_lead', max_length=20, verbose_name='Status')),
                ('stage_changed_at', models.DateTimeField(auto_now_add=True, verbose_name='Stage Changed At')),
                ('scheduled_date', models.DateTimeField(blank=True, null=True, verbose_name='Scheduled Date')),
                ('estimated_duration', models.DurationField(blank=True, null=True, verbose_name='Estimated Duration')),
                ('estimated_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Estimated Cost')),
                ('actual_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Actual Cost')),
                ('ai_priority_score', models.FloatField(default=0.0, help_text='AI-calculated priority score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)], verbose_name='AI Priority Score')),
                ('completion_probability', models.FloatField(default=0.0, help_text='AI-predicted completion probability', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Completion Probability')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('assigned_technician', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_orders', to=settings.AUTH_USER_MODEL, verbose_name='Assigned Technician')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_orders', to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.contact', verbose_name='Contact Person')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.currency', verbose_name='Currency')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_orders', to='hvac_core.customer', verbose_name='Customer')),
                ('deal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_orders', to='hvac_core.deal', verbose_name='Related Deal')),
                ('equipment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_orders', to='hvac_core.equipment', verbose_name='Equipment')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_orders', to='hvac_core.request', verbose_name='Related Request')),
                ('tags', models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags')),
            ],
            options={
                'verbose_name': 'Service Order',
                'verbose_name_plural': 'Service Orders',
                'db_table': 'hvac_service_orders',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='request',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.CreateModel(
            name='Lead',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('middle_name', models.CharField(blank=True, default='', max_length=100, verbose_name='Middle Name')),
                ('email', models.EmailField(blank=True, default='', max_length=254, verbose_name='Email')),
                ('phone', models.CharField(blank=True, default='', max_length=20, verbose_name='Phone')),
                ('mobile', models.CharField(blank=True, default='', max_length=20, verbose_name='Mobile')),
                ('company_name', models.CharField(blank=True, default='', max_length=200, verbose_name='Company Name')),
                ('company_phone', models.CharField(blank=True, default='', max_length=20, verbose_name='Company Phone')),
                ('company_email', models.EmailField(blank=True, default='', max_length=254, verbose_name='Company Email')),
                ('company_address', models.TextField(blank=True, default='', verbose_name='Company Address')),
                ('website', models.URLField(blank=True, default='')),
                ('street_address', models.CharField(blank=True, default='', max_length=200)),
                ('postal_code', models.CharField(blank=True, default='', max_length=10)),
                ('hvac_interest', models.TextField(blank=True, default='', verbose_name='HVAC Interest')),
                ('budget_range', models.CharField(blank=True, default='', max_length=50, verbose_name='Budget Range')),
                ('project_timeline', models.CharField(blank=True, default='', max_length=100, verbose_name='Project Timeline')),
                ('ai_score', models.FloatField(default=0.0, help_text='AI-calculated lead score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('conversion_probability', models.FloatField(default=0.0, help_text='AI-predicted conversion probability (0-1)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('disqualified', models.BooleanField(default=False, verbose_name='Disqualified')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.city', verbose_name='City')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hvac_core.contact', verbose_name='Contact')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.country', verbose_name='Country')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('industry', models.ManyToManyField(blank=True, to='hvac_core.industry', verbose_name='Industry of Company')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.leadsource', verbose_name='Lead Source')),
                ('tags', models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags')),
                ('type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.clienttype', verbose_name='Type of Company')),
            ],
            options={
                'verbose_name': 'Lead',
                'verbose_name_plural': 'Leads',
                'db_table': 'hvac_leads',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='equipment',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.AddField(
            model_name='deal',
            name='lead',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hvac_core.lead', verbose_name='Original Lead'),
        ),
        migrations.AddField(
            model_name='deal',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner'),
        ),
        migrations.AddField(
            model_name='deal',
            name='request',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hvac_core.request', verbose_name='Related Request'),
        ),
        migrations.AddField(
            model_name='deal',
            name='stage',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.stage', verbose_name='Stage'),
        ),
        migrations.AddField(
            model_name='deal',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.AddField(
            model_name='customer',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.CreateModel(
            name='CrmEmail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('subject', models.CharField(max_length=500, verbose_name='Subject')),
                ('from_email', models.EmailField(max_length=254, verbose_name='From')),
                ('to_email', models.TextField(verbose_name='To')),
                ('cc_email', models.TextField(blank=True, default='', verbose_name='CC')),
                ('bcc_email', models.TextField(blank=True, default='', verbose_name='BCC')),
                ('body_text', models.TextField(blank=True, default='', verbose_name='Text Body')),
                ('body_html', models.TextField(blank=True, default='', verbose_name='HTML Body')),
                ('message_id', models.CharField(max_length=255, unique=True, verbose_name='Message ID')),
                ('thread_id', models.CharField(blank=True, default='', max_length=255, verbose_name='Thread ID')),
                ('sent_date', models.DateTimeField(verbose_name='Sent Date')),
                ('received_date', models.DateTimeField(auto_now_add=True, verbose_name='Received Date')),
                ('ai_sentiment', models.CharField(blank=True, choices=[('positive', 'Positive'), ('neutral', 'Neutral'), ('negative', 'Negative')], default='', max_length=20, verbose_name='AI Sentiment')),
                ('ai_priority', models.FloatField(default=0.0, help_text='AI-calculated priority score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)], verbose_name='AI Priority')),
                ('ai_keywords', models.TextField(blank=True, default='', verbose_name='AI Keywords')),
                ('is_outbound', models.BooleanField(default=False, verbose_name='Outbound')),
                ('is_processed', models.BooleanField(default=False, verbose_name='Processed')),
                ('has_attachments', models.BooleanField(default=False, verbose_name='Has Attachments')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.company', verbose_name='Company')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.contact', verbose_name='Contact')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.customer', verbose_name='Customer')),
                ('deal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.deal', verbose_name='Deal')),
                ('lead', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.lead', verbose_name='Lead')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.request', verbose_name='Request')),
                ('service_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_emails', to='hvac_core.serviceorder', verbose_name='Service Order')),
            ],
            options={
                'verbose_name': 'CRM Email',
                'verbose_name_plural': 'CRM Emails',
                'db_table': 'hvac_emails',
                'ordering': ['-sent_date'],
            },
        ),
        migrations.AddField(
            model_name='contact',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.country', verbose_name='Country'),
        ),
        migrations.AddField(
            model_name='contact',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='contact',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner'),
        ),
        migrations.AddField(
            model_name='contact',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.AddField(
            model_name='contact',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.clienttype', verbose_name='Type'),
        ),
        migrations.AddField(
            model_name='company',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.country', verbose_name='Country'),
        ),
        migrations.AddField(
            model_name='company',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='company',
            name='industry',
            field=models.ManyToManyField(blank=True, to='hvac_core.industry', verbose_name='Industry'),
        ),
        migrations.AddField(
            model_name='company',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owned', to=settings.AUTH_USER_MODEL, verbose_name='Owner'),
        ),
        migrations.AddField(
            model_name='company',
            name='tags',
            field=models.ManyToManyField(blank=True, to='hvac_core.tag', verbose_name='Tags'),
        ),
        migrations.AddField(
            model_name='company',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hvac_core.clienttype', verbose_name='Type of Company'),
        ),
        migrations.AddField(
            model_name='city',
            name='country',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='hvac_core.country'),
        ),
        migrations.AddIndex(
            model_name='serviceorder',
            index=models.Index(fields=['customer', 'status'], name='hvac_servic_custome_f04cf2_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceorder',
            index=models.Index(fields=['status', 'priority'], name='hvac_servic_status_8ec1ab_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceorder',
            index=models.Index(fields=['assigned_technician', 'scheduled_date'], name='hvac_servic_assigne_23bf3a_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceorder',
            index=models.Index(fields=['order_number'], name='hvac_servic_order_n_ae9206_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceorder',
            index=models.Index(fields=['ai_priority_score'], name='hvac_servic_ai_prio_360cc4_idx'),
        ),
        migrations.AddIndex(
            model_name='request',
            index=models.Index(fields=['status', 'priority'], name='hvac_reques_status_80490b_idx'),
        ),
        migrations.AddIndex(
            model_name='request',
            index=models.Index(fields=['customer'], name='hvac_reques_custome_ea761b_idx'),
        ),
        migrations.AddIndex(
            model_name='request',
            index=models.Index(fields=['ai_urgency_score'], name='hvac_reques_ai_urge_28e797_idx'),
        ),
        migrations.AddIndex(
            model_name='lead',
            index=models.Index(fields=['email'], name='hvac_leads_email_3b02ac_idx'),
        ),
        migrations.AddIndex(
            model_name='lead',
            index=models.Index(fields=['ai_score'], name='hvac_leads_ai_scor_6b7d56_idx'),
        ),
        migrations.AddIndex(
            model_name='lead',
            index=models.Index(fields=['disqualified'], name='hvac_leads_disqual_f5c191_idx'),
        ),
        migrations.AddIndex(
            model_name='equipment',
            index=models.Index(fields=['customer', 'status'], name='hvac_equipm_custome_dcbe76_idx'),
        ),
        migrations.AddIndex(
            model_name='equipment',
            index=models.Index(fields=['brand', 'model'], name='hvac_equipm_brand_92f5ed_idx'),
        ),
        migrations.AddIndex(
            model_name='equipment',
            index=models.Index(fields=['serial_number'], name='hvac_equipm_serial__a01452_idx'),
        ),
        migrations.AddIndex(
            model_name='equipment',
            index=models.Index(fields=['next_maintenance_date'], name='hvac_equipm_next_ma_ad469c_idx'),
        ),
        migrations.AddIndex(
            model_name='equipment',
            index=models.Index(fields=['health_score'], name='hvac_equipm_health__42430f_idx'),
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['stage', 'active'], name='hvac_deals_stage_i_c3b15a_idx'),
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['customer'], name='hvac_deals_custome_efba42_idx'),
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['closing_date'], name='hvac_deals_closing_25640b_idx'),
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['ai_win_probability'], name='hvac_deals_ai_win__72b789_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='hvac_custom_email_246b6d_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['city', 'district'], name='hvac_custom_city_id_da59a2_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_type'], name='hvac_custom_custome_e76c83_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['ai_health_score'], name='hvac_custom_ai_heal_14d4da_idx'),
        ),
        migrations.AddIndex(
            model_name='crmemail',
            index=models.Index(fields=['from_email'], name='hvac_emails_from_em_e60eb7_idx'),
        ),
        migrations.AddIndex(
            model_name='crmemail',
            index=models.Index(fields=['customer'], name='hvac_emails_custome_157bbd_idx'),
        ),
        migrations.AddIndex(
            model_name='crmemail',
            index=models.Index(fields=['sent_date'], name='hvac_emails_sent_da_04ba93_idx'),
        ),
        migrations.AddIndex(
            model_name='crmemail',
            index=models.Index(fields=['ai_priority'], name='hvac_emails_ai_prio_91062a_idx'),
        ),
        migrations.AddIndex(
            model_name='crmemail',
            index=models.Index(fields=['is_processed'], name='hvac_emails_is_proc_7493c7_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['email'], name='hvac_contac_email_f60251_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['company'], name='hvac_contac_company_ba6339_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['ai_engagement_score'], name='hvac_contac_ai_enga_8a7dc5_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['full_name'], name='hvac_compan_full_na_56dd87_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['city'], name='hvac_compan_city_id_c4a284_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['ai_health_score'], name='hvac_compan_ai_heal_f5b000_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='company',
            unique_together={('full_name', 'country')},
        ),
        migrations.AlterUniqueTogether(
            name='city',
            unique_together={('name', 'country')},
        ),
    ]

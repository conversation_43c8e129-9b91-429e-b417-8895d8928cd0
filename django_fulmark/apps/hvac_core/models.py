"""
Core HVAC business models for Fulmark CRM.
Integrates python_mixer functionality into Django ORM.
Enhanced with comprehensive CRM features inspired by Django CRM.
"""

from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericRelation
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe
from decimal import Decimal
import uuid


# ============================================================================
# BASE MODELS AND SUPPORTING CLASSES
# ============================================================================

class BaseModel(models.Model):
    """Base model with common fields for all CRM entities."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="%(app_label)s_%(class)s_created"
    )
    owner = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="%(app_label)s_%(class)s_owned",
        verbose_name=_("Owner")
    )

    class Meta:
        abstract = True


class Country(models.Model):
    """Country model for address information."""

    name = models.CharField(max_length=100, unique=True, verbose_name=_("Country"))
    code = models.CharField(max_length=3, unique=True, verbose_name=_("Code"))

    class Meta:
        verbose_name = _("Country")
        verbose_name_plural = _("Countries")
        ordering = ['name']

    def __str__(self):
        return self.name


class City(models.Model):
    """City model for address information."""

    name = models.CharField(max_length=100, verbose_name=_("City"))
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='cities')

    class Meta:
        verbose_name = _("City")
        verbose_name_plural = _("Cities")
        unique_together = (('name', 'country'),)
        ordering = ['name']

    def __str__(self):
        return f"{self.name}, {self.country.name}"


class Industry(models.Model):
    """Industry classification for companies."""

    name = models.CharField(max_length=100, unique=True, verbose_name=_("Industry"))
    description = models.TextField(blank=True, verbose_name=_("Description"))

    class Meta:
        verbose_name = _("Industry")
        verbose_name_plural = _("Industries")
        ordering = ['name']

    def __str__(self):
        return self.name


class ClientType(models.Model):
    """Client type classification."""

    name = models.CharField(max_length=50, unique=True, verbose_name=_("Client Type"))
    description = models.TextField(blank=True, verbose_name=_("Description"))

    class Meta:
        verbose_name = _("Client Type")
        verbose_name_plural = _("Client Types")
        ordering = ['name']

    def __str__(self):
        return self.name


class LeadSource(models.Model):
    """Lead source tracking."""

    name = models.CharField(max_length=100, unique=True, verbose_name=_("Lead Source"))
    description = models.TextField(blank=True, verbose_name=_("Description"))

    class Meta:
        verbose_name = _("Lead Source")
        verbose_name_plural = _("Lead Sources")
        ordering = ['name']

    def __str__(self):
        return self.name


class Stage(models.Model):
    """Deal/Pipeline stages."""

    name = models.CharField(max_length=50, unique=True, verbose_name=_("Stage"))
    description = models.TextField(blank=True, verbose_name=_("Description"))
    order = models.PositiveIntegerField(default=0, verbose_name=_("Order"))
    is_active = models.BooleanField(default=True, verbose_name=_("Active"))

    class Meta:
        verbose_name = _("Stage")
        verbose_name_plural = _("Stages")
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


class ClosingReason(models.Model):
    """Reasons for closing deals."""

    name = models.CharField(max_length=100, unique=True, verbose_name=_("Closing Reason"))
    is_positive = models.BooleanField(default=True, verbose_name=_("Positive Outcome"))

    class Meta:
        verbose_name = _("Closing Reason")
        verbose_name_plural = _("Closing Reasons")
        ordering = ['name']

    def __str__(self):
        return self.name


class Currency(models.Model):
    """Currency model for financial transactions."""

    name = models.CharField(max_length=50, verbose_name=_("Currency Name"))
    code = models.CharField(max_length=3, unique=True, verbose_name=_("Currency Code"))
    symbol = models.CharField(max_length=5, verbose_name=_("Symbol"))
    rate_to_pln = models.DecimalField(
        max_digits=10, decimal_places=4, default=Decimal('1.0000'),
        verbose_name=_("Rate to PLN")
    )

    class Meta:
        verbose_name = _("Currency")
        verbose_name_plural = _("Currencies")
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Tag(models.Model):
    """Tags for categorizing CRM objects."""

    name = models.CharField(max_length=50, unique=True, verbose_name=_("Tag"))
    color = models.CharField(max_length=7, default='#007bff', verbose_name=_("Color"))

    class Meta:
        verbose_name = _("Tag")
        verbose_name_plural = _("Tags")
        ordering = ['name']

    def __str__(self):
        return self.name


# ============================================================================
# CORE CRM MODELS
# ============================================================================

class Company(BaseModel):
    """Company model with HVAC business focus."""

    full_name = models.CharField(
        max_length=200, null=False, blank=False,
        verbose_name=_("Company Name")
    )
    alternative_names = models.CharField(
        max_length=200, blank=True, default='',
        verbose_name=_("Alternative Names"),
        help_text=_("Separate them with commas.")
    )
    website = models.URLField(
        max_length=200, blank=True, default='',
        verbose_name=_("Website")
    )
    phone = models.CharField(
        max_length=100, blank=True, default='',
        verbose_name=_("Phone")
    )
    email = models.EmailField(
        blank=True, default='',
        verbose_name=_("Email")
    )

    # Address Information
    street_address = models.CharField(max_length=200, blank=True, default='')
    city = models.ForeignKey(
        City, blank=True, null=True,
        verbose_name=_("City"), on_delete=models.SET_NULL
    )
    postal_code = models.CharField(max_length=10, blank=True, default='')
    country = models.ForeignKey(
        Country, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Country")
    )

    # Business Information
    registration_number = models.CharField(
        max_length=30, default='', blank=True,
        verbose_name=_("Registration Number")
    )
    type = models.ForeignKey(
        ClientType, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Type of Company")
    )
    industry = models.ManyToManyField(
        Industry, blank=True,
        verbose_name=_("Industry")
    )

    # HVAC Specific
    hvac_systems_count = models.PositiveIntegerField(
        default=0, verbose_name=_("HVAC Systems Count")
    )
    annual_maintenance_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        verbose_name=_("Annual Maintenance Value")
    )

    # AI Enhanced Fields
    ai_health_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated company health score (0-100)"
    )

    # Status
    active = models.BooleanField(default=True, verbose_name=_("Active"))
    disqualified = models.BooleanField(default=False, verbose_name=_("Disqualified"))

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_companies'
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")
        unique_together = (('full_name', 'country'),)
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['full_name']),
            models.Index(fields=['city']),
            models.Index(fields=['ai_health_score']),
        ]

    def __str__(self):
        return self.full_name

    def get_absolute_url(self):
        return reverse('admin:hvac_core_company_change', args=(self.id,))


class Contact(BaseModel):
    """Contact person model - can be linked to companies."""

    # Basic Information
    first_name = models.CharField(max_length=100, verbose_name=_("First Name"))
    last_name = models.CharField(max_length=100, verbose_name=_("Last Name"))
    middle_name = models.CharField(max_length=100, blank=True, default='', verbose_name=_("Middle Name"))

    # Contact Information
    email = models.EmailField(blank=True, default='', verbose_name=_("Email"))
    phone = models.CharField(max_length=20, blank=True, default='', verbose_name=_("Phone"))
    mobile = models.CharField(max_length=20, blank=True, default='', verbose_name=_("Mobile"))

    # Position Information
    position = models.CharField(max_length=100, blank=True, default='', verbose_name=_("Position"))
    department = models.CharField(max_length=100, blank=True, default='', verbose_name=_("Department"))

    # Address Information
    street_address = models.CharField(max_length=200, blank=True, default='')
    city = models.ForeignKey(
        City, blank=True, null=True,
        verbose_name=_("City"), on_delete=models.SET_NULL
    )
    postal_code = models.CharField(max_length=10, blank=True, default='')
    country = models.ForeignKey(
        Country, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Country")
    )

    # Business Relations
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.CASCADE,
        related_name='contacts', verbose_name=_("Company")
    )
    type = models.ForeignKey(
        ClientType, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Type")
    )

    # HVAC Specific
    is_decision_maker = models.BooleanField(default=False, verbose_name=_("Decision Maker"))
    technical_contact = models.BooleanField(default=False, verbose_name=_("Technical Contact"))

    # AI Enhanced Fields
    ai_engagement_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated engagement score (0-100)"
    )

    # Status
    active = models.BooleanField(default=True, verbose_name=_("Active"))
    disqualified = models.BooleanField(default=False, verbose_name=_("Disqualified"))

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_contacts'
        verbose_name = _("Contact")
        verbose_name_plural = _("Contacts")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['company']),
            models.Index(fields=['ai_engagement_score']),
        ]

    def __str__(self):
        if self.company:
            return f"{self.full_name} ({self.company.full_name})"
        return self.full_name

    @property
    def full_name(self):
        full_name = ' '.join(filter(None, (self.first_name, self.middle_name, self.last_name)))
        if self.disqualified:
            full_name = f"({_('Disqualified')}) {full_name}"
        return full_name

    def get_absolute_url(self):
        return reverse('admin:hvac_core_contact_change', args=(self.id,))


class Lead(BaseModel):
    """Lead model for potential customers."""

    # Basic Information
    first_name = models.CharField(max_length=100, verbose_name=_("First Name"))
    last_name = models.CharField(max_length=100, verbose_name=_("Last Name"))
    middle_name = models.CharField(max_length=100, blank=True, default='', verbose_name=_("Middle Name"))

    # Contact Information
    email = models.EmailField(blank=True, default='', verbose_name=_("Email"))
    phone = models.CharField(max_length=20, blank=True, default='', verbose_name=_("Phone"))
    mobile = models.CharField(max_length=20, blank=True, default='', verbose_name=_("Mobile"))

    # Company Information
    company_name = models.CharField(max_length=200, blank=True, default='', verbose_name=_("Company Name"))
    company_phone = models.CharField(max_length=20, blank=True, default='', verbose_name=_("Company Phone"))
    company_email = models.EmailField(blank=True, default='', verbose_name=_("Company Email"))
    company_address = models.TextField(blank=True, default='', verbose_name=_("Company Address"))
    website = models.URLField(max_length=200, blank=True, default='')

    # Address Information
    street_address = models.CharField(max_length=200, blank=True, default='')
    city = models.ForeignKey(
        City, blank=True, null=True,
        verbose_name=_("City"), on_delete=models.SET_NULL
    )
    postal_code = models.CharField(max_length=10, blank=True, default='')
    country = models.ForeignKey(
        Country, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Country")
    )

    # Business Information
    type = models.ForeignKey(
        ClientType, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Type of Company")
    )
    industry = models.ManyToManyField(
        Industry, blank=True,
        verbose_name=_("Industry of Company")
    )
    source = models.ForeignKey(
        LeadSource, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Lead Source")
    )

    # HVAC Specific
    hvac_interest = models.TextField(blank=True, default='', verbose_name=_("HVAC Interest"))
    budget_range = models.CharField(max_length=50, blank=True, default='', verbose_name=_("Budget Range"))
    project_timeline = models.CharField(max_length=100, blank=True, default='', verbose_name=_("Project Timeline"))

    # AI Enhanced Fields
    ai_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated lead score (0-100)"
    )
    conversion_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted conversion probability (0-1)"
    )

    # Status
    disqualified = models.BooleanField(default=False, verbose_name=_("Disqualified"))

    # Relations
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name=_("Contact")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name=_("Company")
    )
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_leads'
        verbose_name = _("Lead")
        verbose_name_plural = _("Leads")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['ai_score']),
            models.Index(fields=['disqualified']),
        ]

    def __str__(self):
        if self.company_name:
            return f"{self.full_name}, {self.company_name}, {self.country}"
        return self.full_name

    @property
    def full_name(self):
        full_name = ' '.join(filter(None, (self.first_name, self.middle_name, self.last_name)))
        if self.disqualified:
            full_name = f"({_('Disqualified')}) {full_name}"
        return full_name

    def get_absolute_url(self):
        return reverse('admin:hvac_core_lead_change', args=(self.id,))


class Customer(BaseModel):
    """Enhanced customer model - converted leads/contacts."""

    CUSTOMER_TYPE_CHOICES = [
        ('residential', 'Residential'),
        ('commercial', 'Commercial'),
        ('industrial', 'Industrial'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    # Basic Information
    first_name = models.CharField(max_length=100, verbose_name=_("First Name"))
    last_name = models.CharField(max_length=100, verbose_name=_("Last Name"))
    email = models.EmailField(unique=True, verbose_name=_("Email"))
    phone = models.CharField(max_length=20, verbose_name=_("Phone"))

    # Address Information
    street_address = models.CharField(max_length=200)
    city = models.ForeignKey(
        City, blank=True, null=True,
        verbose_name=_("City"), on_delete=models.SET_NULL
    )
    postal_code = models.CharField(max_length=10)
    district = models.CharField(max_length=50, blank=True, null=True)  # Warsaw districts
    country = models.ForeignKey(
        Country, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Country")
    )

    # Business Information
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPE_CHOICES, default='residential')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Relations
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='customers', verbose_name=_("Company")
    )
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='customers', verbose_name=_("Primary Contact")
    )

    # AI-Enhanced Fields
    ai_health_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated customer health score (0-100)"
    )
    churn_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted churn probability (0-1)"
    )
    lifetime_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="AI-estimated customer lifetime value"
    )

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_customers'
        verbose_name = _("Customer")
        verbose_name_plural = _("Customers")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['city', 'district']),
            models.Index(fields=['customer_type']),
            models.Index(fields=['ai_health_score']),
        ]

    def __str__(self):
        if self.company:
            return f"{self.company.full_name} ({self.full_name})"
        return self.full_name

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_address(self):
        city_name = self.city.name if self.city else ''
        return f"{self.street_address}, {self.postal_code} {city_name}"

    def get_absolute_url(self):
        return reverse('admin:hvac_core_customer_change', args=(self.id,))


class Request(BaseModel):
    """Customer requests/inquiries."""

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('new', 'New'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    # Basic Information
    title = models.CharField(max_length=200, verbose_name=_("Title"))
    description = models.TextField(verbose_name=_("Description"))
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')

    # Relations
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE,
        related_name='requests', verbose_name=_("Customer")
    )
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Contact Person")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Company")
    )

    # HVAC Specific
    hvac_system_type = models.CharField(max_length=100, blank=True, default='')
    estimated_budget = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        verbose_name=_("Estimated Budget")
    )

    # AI Enhanced
    ai_urgency_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated urgency score"
    )

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_requests'
        verbose_name = _("Request")
        verbose_name_plural = _("Requests")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['customer']),
            models.Index(fields=['ai_urgency_score']),
        ]

    def __str__(self):
        return f"{self.title} - {self.customer.full_name}"

    def get_absolute_url(self):
        return reverse('admin:hvac_core_request_change', args=(self.id,))


class Deal(BaseModel):
    """Deal model with 7-stage pipeline."""

    name = models.CharField(
        max_length=250, null=False, blank=False,
        verbose_name=_("Deal Name")
    )
    description = models.TextField(blank=True, default='', verbose_name=_("Description"))

    # Pipeline Management
    stage = models.ForeignKey(
        Stage, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Stage")
    )
    next_step = models.CharField(
        max_length=250, blank=True, default='',
        verbose_name=_("Next Step")
    )
    next_step_date = models.DateField(
        null=True, blank=True,
        verbose_name=_("Next Step Date")
    )

    # Financial Information
    amount = models.DecimalField(
        blank=True, null=True, default=0,
        max_digits=10, decimal_places=2,
        verbose_name=_("Amount")
    )
    currency = models.ForeignKey(
        Currency, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Currency")
    )
    probability = models.PositiveSmallIntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_("Probability (%)")
    )

    # Dates
    closing_date = models.DateField(
        blank=True, null=True,
        verbose_name=_("Expected Closing Date")
    )
    win_closing_date = models.DateTimeField(
        blank=True, null=True,
        verbose_name=_("Actual Win Date")
    )

    # Relations
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE,
        related_name='deals', verbose_name=_("Customer")
    )
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Primary Contact")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='deals', verbose_name=_("Company")
    )
    lead = models.ForeignKey(
        Lead, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name=_("Original Lead")
    )
    request = models.ForeignKey(
        Request, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name=_("Related Request")
    )

    # Status
    closing_reason = models.ForeignKey(
        ClosingReason, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Closing Reason")
    )
    active = models.BooleanField(default=True, verbose_name=_("Active"))
    important = models.BooleanField(default=False, verbose_name=_("Important"))

    # AI Enhanced
    ai_win_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted win probability"
    )

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_deals'
        verbose_name = _("Deal")
        verbose_name_plural = _("Deals")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['stage', 'active']),
            models.Index(fields=['customer']),
            models.Index(fields=['closing_date']),
            models.Index(fields=['ai_win_probability']),
        ]

    def __str__(self):
        return f"{self.name} - {self.customer.full_name}"

    def get_absolute_url(self):
        return reverse('admin:hvac_core_deal_change', args=(self.id,))


class Equipment(BaseModel):
    """HVAC equipment with lifecycle tracking."""

    EQUIPMENT_TYPE_CHOICES = [
        ('split_ac', 'Split Air Conditioner'),
        ('multi_split', 'Multi-Split System'),
        ('vrf', 'VRF System'),
        ('chiller', 'Chiller'),
        ('heat_pump', 'Heat Pump'),
        ('ventilation', 'Ventilation System'),
        ('air_handler', 'Air Handler'),
        ('condenser', 'Condenser Unit'),
        ('other', 'Other'),
    ]

    BRAND_CHOICES = [
        ('lg', 'LG'),
        ('daikin', 'Daikin'),
        ('mitsubishi', 'Mitsubishi'),
        ('samsung', 'Samsung'),
        ('panasonic', 'Panasonic'),
        ('carrier', 'Carrier'),
        ('trane', 'Trane'),
        ('york', 'York'),
        ('other', 'Other'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('maintenance', 'Under Maintenance'),
        ('warranty', 'Under Warranty'),
        ('retired', 'Retired'),
        ('faulty', 'Faulty'),
        ('pending_install', 'Pending Installation'),
    ]

    # Relations
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE,
        related_name='equipment', verbose_name=_("Customer")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='equipment', verbose_name=_("Company")
    )

    # Equipment Details
    equipment_type = models.CharField(
        max_length=20, choices=EQUIPMENT_TYPE_CHOICES,
        verbose_name=_("Equipment Type")
    )
    brand = models.CharField(
        max_length=20, choices=BRAND_CHOICES,
        verbose_name=_("Brand")
    )
    model = models.CharField(max_length=100, verbose_name=_("Model"))
    serial_number = models.CharField(
        max_length=100, unique=True,
        verbose_name=_("Serial Number")
    )
    capacity_btu = models.IntegerField(
        help_text="Capacity in BTU/h",
        verbose_name=_("Capacity (BTU/h)")
    )

    # Installation Information
    installation_date = models.DateField(verbose_name=_("Installation Date"))
    installation_location = models.CharField(
        max_length=200, verbose_name=_("Installation Location")
    )
    installer = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name=_("Installer")
    )

    # Lifecycle Information
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='active',
        verbose_name=_("Status")
    )
    warranty_end_date = models.DateField(
        null=True, blank=True,
        verbose_name=_("Warranty End Date")
    )
    last_maintenance_date = models.DateField(
        null=True, blank=True,
        verbose_name=_("Last Maintenance Date")
    )
    next_maintenance_date = models.DateField(
        null=True, blank=True,
        verbose_name=_("Next Maintenance Date")
    )

    # Financial
    purchase_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        verbose_name=_("Purchase Price")
    )
    installation_cost = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        verbose_name=_("Installation Cost")
    )

    # AI-Enhanced Fields
    health_score = models.FloatField(
        default=100.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated equipment health score",
        verbose_name=_("Health Score")
    )
    failure_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted failure probability",
        verbose_name=_("Failure Probability")
    )

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_equipment'
        verbose_name = _("Equipment")
        verbose_name_plural = _("Equipment")
        ordering = ['-installation_date']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['brand', 'model']),
            models.Index(fields=['serial_number']),
            models.Index(fields=['next_maintenance_date']),
            models.Index(fields=['health_score']),
        ]

    def __str__(self):
        return f"{self.brand} {self.model} - {self.customer.full_name}"

    @property
    def age_years(self):
        """Calculate equipment age in years."""
        return (timezone.now().date() - self.installation_date).days / 365.25

    @property
    def is_under_warranty(self):
        """Check if equipment is under warranty."""
        if not self.warranty_end_date:
            return False
        return timezone.now().date() <= self.warranty_end_date

    @property
    def maintenance_overdue(self):
        """Check if maintenance is overdue."""
        if not self.next_maintenance_date:
            return False
        return timezone.now().date() > self.next_maintenance_date

    def get_absolute_url(self):
        return reverse('admin:hvac_core_equipment_change', args=(self.id,))


class ServiceOrder(BaseModel):
    """Service orders with enhanced pipeline management."""

    STATUS_CHOICES = [
        ('new_lead', 'New Lead'),
        ('qualified', 'Qualified'),
        ('proposal', 'Proposal'),
        ('negotiation', 'Negotiation'),
        ('in_progress', 'In Progress'),
        ('closed_won', 'Closed Won'),
        ('follow_up', 'Follow Up'),
        ('cancelled', 'Cancelled'),
    ]

    SERVICE_TYPE_CHOICES = [
        ('installation', 'Installation'),
        ('maintenance', 'Maintenance'),
        ('repair', 'Repair'),
        ('inspection', 'Inspection'),
        ('consultation', 'Consultation'),
        ('warranty', 'Warranty Service'),
        ('emergency', 'Emergency Service'),
        ('upgrade', 'System Upgrade'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('emergency', 'Emergency'),
    ]

    # Basic Information
    order_number = models.CharField(
        max_length=20, unique=True,
        verbose_name=_("Order Number")
    )
    title = models.CharField(max_length=200, verbose_name=_("Title"))
    description = models.TextField(verbose_name=_("Description"))

    # Relations
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE,
        related_name='service_orders', verbose_name=_("Customer")
    )
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Contact Person")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='service_orders', verbose_name=_("Company")
    )
    equipment = models.ForeignKey(
        Equipment, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='service_orders', verbose_name=_("Equipment")
    )
    deal = models.ForeignKey(
        Deal, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='service_orders', verbose_name=_("Related Deal")
    )
    request = models.ForeignKey(
        Request, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='service_orders', verbose_name=_("Related Request")
    )

    # Service Details
    service_type = models.CharField(
        max_length=20, choices=SERVICE_TYPE_CHOICES,
        verbose_name=_("Service Type")
    )
    priority = models.CharField(
        max_length=10, choices=PRIORITY_CHOICES, default='medium',
        verbose_name=_("Priority")
    )

    # Pipeline Status
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='new_lead',
        verbose_name=_("Status")
    )
    stage_changed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Stage Changed At")
    )

    # Scheduling
    scheduled_date = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_("Scheduled Date")
    )
    estimated_duration = models.DurationField(
        null=True, blank=True,
        verbose_name=_("Estimated Duration")
    )
    assigned_technician = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_orders',
        verbose_name=_("Assigned Technician")
    )

    # Financial
    estimated_cost = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        verbose_name=_("Estimated Cost")
    )
    actual_cost = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        verbose_name=_("Actual Cost")
    )
    currency = models.ForeignKey(
        Currency, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name=_("Currency")
    )

    # AI-Enhanced Fields
    ai_priority_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated priority score",
        verbose_name=_("AI Priority Score")
    )
    completion_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted completion probability",
        verbose_name=_("Completion Probability")
    )

    # Completion
    completed_at = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_("Completed At")
    )

    # Relations
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_("Tags"))

    class Meta:
        db_table = 'hvac_service_orders'
        verbose_name = _("Service Order")
        verbose_name_plural = _("Service Orders")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['assigned_technician', 'scheduled_date']),
            models.Index(fields=['order_number']),
            models.Index(fields=['ai_priority_score']),
        ]

    def __str__(self):
        return f"{self.order_number} - {self.title}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

    def generate_order_number(self):
        """Generate unique order number."""
        from datetime import datetime
        prefix = "FUL"
        date_part = datetime.now().strftime("%Y%m%d")
        count = ServiceOrder.objects.filter(
            order_number__startswith=f"{prefix}{date_part}"
        ).count() + 1
        return f"{prefix}{date_part}{count:03d}"

    def get_absolute_url(self):
        return reverse('admin:hvac_core_serviceorder_change', args=(self.id,))


# ============================================================================
# ADDITIONAL MODELS FOR EMAIL AND COMMUNICATION
# ============================================================================

class CrmEmail(BaseModel):
    """Email messages linked to CRM objects."""

    # Email Headers
    subject = models.CharField(max_length=500, verbose_name=_("Subject"))
    from_email = models.EmailField(verbose_name=_("From"))
    to_email = models.TextField(verbose_name=_("To"))
    cc_email = models.TextField(blank=True, default='', verbose_name=_("CC"))
    bcc_email = models.TextField(blank=True, default='', verbose_name=_("BCC"))

    # Content
    body_text = models.TextField(blank=True, default='', verbose_name=_("Text Body"))
    body_html = models.TextField(blank=True, default='', verbose_name=_("HTML Body"))

    # Email Metadata
    message_id = models.CharField(max_length=255, unique=True, verbose_name=_("Message ID"))
    thread_id = models.CharField(max_length=255, blank=True, default='', verbose_name=_("Thread ID"))
    sent_date = models.DateTimeField(verbose_name=_("Sent Date"))
    received_date = models.DateTimeField(auto_now_add=True, verbose_name=_("Received Date"))

    # Relations to CRM Objects
    customer = models.ForeignKey(
        Customer, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Customer")
    )
    contact = models.ForeignKey(
        Contact, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Contact")
    )
    company = models.ForeignKey(
        Company, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Company")
    )
    lead = models.ForeignKey(
        Lead, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Lead")
    )
    deal = models.ForeignKey(
        Deal, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Deal")
    )
    request = models.ForeignKey(
        Request, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Request")
    )
    service_order = models.ForeignKey(
        ServiceOrder, blank=True, null=True, on_delete=models.SET_NULL,
        related_name='crm_emails', verbose_name=_("Service Order")
    )

    # AI Analysis
    ai_sentiment = models.CharField(
        max_length=20, blank=True, default='',
        choices=[('positive', 'Positive'), ('neutral', 'Neutral'), ('negative', 'Negative')],
        verbose_name=_("AI Sentiment")
    )
    ai_priority = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated priority score",
        verbose_name=_("AI Priority")
    )
    ai_keywords = models.TextField(blank=True, default='', verbose_name=_("AI Keywords"))

    # Status
    is_outbound = models.BooleanField(default=False, verbose_name=_("Outbound"))
    is_processed = models.BooleanField(default=False, verbose_name=_("Processed"))
    has_attachments = models.BooleanField(default=False, verbose_name=_("Has Attachments"))

    class Meta:
        db_table = 'hvac_emails'
        verbose_name = _("CRM Email")
        verbose_name_plural = _("CRM Emails")
        ordering = ['-sent_date']
        indexes = [
            models.Index(fields=['from_email']),
            models.Index(fields=['customer']),
            models.Index(fields=['sent_date']),
            models.Index(fields=['ai_priority']),
            models.Index(fields=['is_processed']),
        ]

    def __str__(self):
        return f"{self.subject} - {self.from_email}"

    def get_absolute_url(self):
        return reverse('admin:hvac_core_crmemail_change', args=(self.id,))

"""
🚀 HVAC CRM Flet Application
Cutting-edge Flet interface for comprehensive backend interaction
"""

import flet as ft
import asyncio
import logging
import threading
import time
from typing import Dict, Any, Optional

from .config import config
from .threading_manager import ThreadingManager, ThreadType
from ..interfaces.dashboard import DashboardInterface
from ..interfaces.customer_profiles import CustomerProfilesInterface
from ..interfaces.email_intelligence import EmailIntelligenceInterface
from ..services.gospine_client import GoSpineClient
from ..utils.real_time_updates import RealTimeUpdater

logger = logging.getLogger(__name__)

class HVACCRMApp:
    """Main HVAC CRM Flet Application"""
    
    def __init__(self):
        self.page: Optional[ft.Page] = None
        self.threading_manager = ThreadingManager(config.ui.max_concurrent_threads)
        self.gospine_client = GoSpineClient()
        self.real_time_updater = RealTimeUpdater()
        
        # Interface components
        self.dashboard = None
        self.customer_profiles = None
        self.email_intelligence = None
        
        # Navigation state
        self.current_view = "dashboard"
        
        logger.info("🚀 HVAC CRM App initialized")
    
    async def main(self, page: ft.Page):
        """Main application entry point"""
        self.page = page
        
        # Configure page
        await self._configure_page()
        
        # Initialize interfaces
        await self._initialize_interfaces()
        
        # Setup navigation
        await self._setup_navigation()
        
        # Start background threads
        await self._start_background_threads()
        
        # Show initial view
        await self._show_dashboard()
        
        logger.info("✅ HVAC CRM App started successfully")
    
    async def _configure_page(self):
        """Configure the main page settings"""
        self.page.title = config.ui.window_title
        self.page.window_width = config.ui.window_width
        self.page.window_height = config.ui.window_height
        self.page.theme_mode = getattr(ft.ThemeMode, config.ui.theme_mode.upper())
        self.page.padding = 0
        self.page.spacing = 0
        
        # Set theme colors
        self.page.theme = ft.Theme(
            color_scheme_seed=config.ui.primary_color,
            use_material3=True
        )
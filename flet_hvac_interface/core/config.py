"""
🔧 HVAC CRM Flet Interface Configuration
Cutting-edge configuration management for the future-proof Flet interface
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
import logging

@dataclass
class DatabaseConfig:
    """Database configuration for multi-database architecture"""
    # PostgreSQL (Operational Data)
    postgres_host: str = "**************"
    postgres_port: int = 5432
    postgres_user: str = "koldbringer"
    postgres_password: str = "Blaeritipol1"
    postgres_database: str = "hvac_crm"
    
    # MongoDB (Unstructured Data)
    mongodb_host: str = "**************"
    mongodb_port: int = 27017
    mongodb_user: str = "Koldbringer"
    mongodb_password: str = "blaeiritpol"
    mongodb_database: str = "hvac_data"
    
    # Redis (Caching & Real-time)
    redis_host: str = "**************"
    redis_port: int = 6379
    redis_password: Optional[str] = None
    redis_db: int = 0
    
    # MinIO (File Storage)
    minio_endpoint: str = "**************:9000"
    minio_access_key: str = "koldbringer"
    minio_secret_key: str = "Blaeritipol1"
    minio_bucket: str = "hvac-documents"

@dataclass
class APIConfig:
    """API configuration for backend integrations"""
    # GoSpine API
    gospine_base_url: str = "http://localhost:8080"
    gospine_api_key: Optional[str] = None
    
    # LM Studio (AI Models)
    lm_studio_base_url: str = "http://*************:1234"
    bielik_v3_endpoint: str = "/v1/chat/completions"
    gemma_endpoint: str = "/v1/chat/completions"    
    # Email Configuration
    email_imap_server: str = "imap.gmail.com"
    email_imap_port: int = 993
    dolores_email: str = "<EMAIL>"
    dolores_password: str = "Blaeritipol1"
    grzegorz_email: str = "<EMAIL>"
    grzegorz_password: str = "Blaeritipol1"

@dataclass
class UIConfig:
    """UI configuration for Flet interface"""
    # Window settings
    window_width: int = 1400
    window_height: int = 900
    window_title: str = "🔧 HVAC CRM - Cosmic Interface"
    
    # Theme settings
    theme_mode: str = "dark"  # "light", "dark", "system"
    primary_color: str = "#2196F3"  # Material Blue
    accent_color: str = "#FF9800"   # Material Orange
    
    # Performance settings
    update_interval: int = 1000  # milliseconds
    max_concurrent_threads: int = 6
    
    # Feature flags
    enable_real_time_updates: bool = True
    enable_ai_analysis: bool = True
    enable_email_monitoring: bool = True
    enable_transcription_processing: bool = True

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/flet_hvac_interface.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

class Config:
    """Main configuration class for HVAC CRM Flet Interface"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.api = APIConfig()
        self.ui = UIConfig()
        self.logging = LoggingConfig()
        
        # Load environment overrides
        self._load_environment_overrides()
        
        # Setup logging
        self._setup_logging()
    
    def _load_environment_overrides(self):
        """Load configuration overrides from environment variables"""
        # Database overrides
        if os.getenv("POSTGRES_HOST"):
            self.database.postgres_host = os.getenv("POSTGRES_HOST")        if os.getenv("POSTGRES_PASSWORD"):
            self.database.postgres_password = os.getenv("POSTGRES_PASSWORD")
        
        # API overrides
        if os.getenv("GOSPINE_BASE_URL"):
            self.api.gospine_base_url = os.getenv("GOSPINE_BASE_URL")
        if os.getenv("LM_STUDIO_BASE_URL"):
            self.api.lm_studio_base_url = os.getenv("LM_STUDIO_BASE_URL")
        
        # UI overrides
        if os.getenv("THEME_MODE"):
            self.ui.theme_mode = os.getenv("THEME_MODE")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        os.makedirs(os.path.dirname(self.logging.file_path), exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.logging.level),
            format=self.logging.format,
            handlers=[
                logging.FileHandler(self.logging.file_path),
                logging.StreamHandler()
            ]
        )
    
    def get_postgres_url(self) -> str:
        """Get PostgreSQL connection URL"""
        return (
            f"postgresql://{self.database.postgres_user}:"
            f"{self.database.postgres_password}@"
            f"{self.database.postgres_host}:"
            f"{self.database.postgres_port}/"
            f"{self.database.postgres_database}"
        )
    
    def get_mongodb_url(self) -> str:
        """Get MongoDB connection URL"""
        return (
            f"mongodb://{self.database.mongodb_user}:"
            f"{self.database.mongodb_password}@"
            f"{self.database.mongodb_host}:"
            f"{self.database.mongodb_port}/"
            f"{self.database.mongodb_database}?authSource=admin"
        )
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL"""
        if self.database.redis_password:
            return (
                f"redis://:{self.database.redis_password}@"
                f"{self.database.redis_host}:"
                f"{self.database.redis_port}/"
                f"{self.database.redis_db}"
            )
        else:
            return (
                f"redis://{self.database.redis_host}:"
                f"{self.database.redis_port}/"
                f"{self.database.redis_db}"
            )

# Global configuration instance
config = Config()

# Export for easy importing
__all__ = ["config", "Config", "DatabaseConfig", "APIConfig", "UIConfig", "LoggingConfig"]
"""
🧵 HVAC CRM Threading Manager
Advanced multi-threading coordination for real-time data processing
"""

import threading
import queue
import logging
from typing import Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ThreadType(Enum):
    """Thread types for the HVAC CRM system"""
    EMAIL_MONITOR = "email_monitor"
    TRANSCRIPTION_PROCESSOR = "transcription_processor"
    AI_ANALYSIS = "ai_analysis"
    DATABASE_SYNC = "database_sync"
    REAL_TIME_UPDATES = "real_time_updates"
    CALENDAR_PROCESSOR = "calendar_processor"

@dataclass
class ThreadMessage:
    """Message structure for inter-thread communication"""
    thread_type: ThreadType
    message_type: str
    data: Any
    timestamp: float
    priority: int = 1  # 1=low, 5=high

class ThreadingManager:
    """Advanced threading manager for HVAC CRM Flet interface"""
    
    def __init__(self, max_threads: int = 6):
        self.max_threads = max_threads
        self.threads: Dict[ThreadType, threading.Thread] = {}
        self.thread_queues: Dict[ThreadType, queue.Queue] = {}
        self.shared_data: Dict[str, Any] = {}
        self.locks: Dict[str, threading.Lock] = {}
        self.events: Dict[str, threading.Event] = {}
        self.running = False
        
        # Initialize queues for each thread type
        for thread_type in ThreadType:
            self.thread_queues[thread_type] = queue.Queue()
        
        # Initialize shared locks
        self.locks['shared_data'] = threading.Lock()
        self.locks['ui_update'] = threading.Lock()
        
        # Initialize events
        self.events['shutdown'] = threading.Event()
        self.events['ui_ready'] = threading.Event()
        
        logger.info("🧵 Threading Manager initialized")
"""
🔧 HVAC CRM Flet Interface - Main Entry Point
Cutting-edge future-proof interface for backend interaction
"""

import flet as ft
import asyncio
import logging
import threading
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HVACCRMInterface:
    """Main HVAC CRM Flet Interface Application"""
    
    def __init__(self):
        self.page = None
        self.current_view = "dashboard"
        self.data_sources_status = {
            "email_monitor": {"status": "running", "last_update": datetime.now()},
            "transcription": {"status": "idle", "last_update": datetime.now()},
            "ai_analysis": {"status": "processing", "last_update": datetime.now()},
            "database_sync": {"status": "syncing", "last_update": datetime.now()}
        }
        
        # Background threads
        self.threads = {}
        self.running = True
        
        logger.info("🚀 HVAC CRM Interface initialized")
    
    def main(self, page: ft.Page):
        """Main application entry point"""
        self.page = page
        
        # Configure page
        page.title = "🔧 HVAC CRM - Cosmic Interface"
        page.window_width = 1400
        page.window_height = 900
        page.theme_mode = ft.ThemeMode.DARK
        page.padding = 0
        
        # Set theme and create layout
        page.theme = ft.Theme(color_scheme_seed="#2196F3", use_material3=True)
        self.create_main_layout()
        self.start_background_threads()
        
        logger.info("✅ HVAC CRM Interface started successfully")
#!/usr/bin/env python3
"""
🗓️ CALENDAR DATA PROCESSING SYSTEM WITH INTELLIGENT SCHEDULING
==============================================================

Advanced calendar processing system that integrates calendar data with customer profiles
and implements intelligent scheduling optimization using semantic understanding.

Features:
- Calendar data processing with semantic analysis
- Intelligent scheduling optimization
- Customer-calendar linking
- Predictive scheduling based on patterns
- Service route optimization
- Conflict detection and resolution
- Integration with Gobeklitepe semantic framework
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
import json
import math
from enum import Enum
import pandas as pd

# Geolocation and optimization
try:
    import geopy
    from geopy.distance import geodesic
    from geopy.geocoders import Nominatim
    GEOPY_AVAILABLE = True
except ImportError:
    GEOPY_AVAILABLE = False
    logger.warning("Geopy not available - geographic optimization disabled")

from enhanced_data_pipeline import CalendarEntry, CustomerProfile
from customer_profile_system import EnhancedCustomerProfile
from integrations.gobeklitepe_bridge import GobeklitepeBridge

logger = logging.getLogger(__name__)


class ScheduleOptimizationStrategy(Enum):
    """Schedule optimization strategies"""
    GEOGRAPHIC = "geographic"
    PRIORITY = "priority"
    CUSTOMER_PREFERENCE = "customer_preference"
    TECHNICIAN_SKILL = "technician_skill"
    EQUIPMENT_TYPE = "equipment_type"


@dataclass
class ServiceAppointment:
    """Enhanced service appointment"""
    appointment_id: str
    customer_id: str
    customer_name: str
    service_type: str
    equipment_type: str
    equipment_brand: str
    address: str
    city: str
    phone: str
    scheduled_date: datetime
    estimated_duration: int  # minutes
    priority_score: float
    technician_assigned: Optional[str] = None
    status: str = "scheduled"
    notes: str = ""
    coordinates: Optional[Tuple[float, float]] = None
    semantic_analysis: Optional[Dict[str, Any]] = None


@dataclass
class TechnicianSchedule:
    """Technician schedule and capabilities"""
    technician_id: str
    name: str
    skills: List[str]
    equipment_specialties: List[str]
    working_hours: Dict[str, Tuple[int, int]]  # day: (start_hour, end_hour)
    max_appointments_per_day: int
    current_location: Optional[Tuple[float, float]] = None
    appointments: List[ServiceAppointment] = field(default_factory=list)


@dataclass
class ScheduleOptimizationResult:
    """Result of schedule optimization"""
    optimized_appointments: List[ServiceAppointment]
    total_travel_time: float
    total_travel_distance: float
    optimization_score: float
    conflicts_resolved: int
    recommendations: List[str]
    optimization_strategy: ScheduleOptimizationStrategy


class GeographicOptimizer:
    """Geographic optimization for service routes"""
    
    def __init__(self):
        self.geocoder = None
        if GEOPY_AVAILABLE:
            self.geocoder = Nominatim(user_agent="hvac_crm_scheduler")
        
        # Warsaw district coordinates (approximate centers)
        self.warsaw_districts = {
            'mokotów': (52.1800, 21.0450),
            'wilanów': (52.1650, 21.0900),
            'ursynów': (52.1400, 21.0450),
            'ochota': (52.2100, 21.0100),
            'wola': (52.2400, 20.9800),
            'śródmieście': (52.2300, 21.0100),
            'praga': (52.2500, 21.0400),
            'bemowo': (52.2600, 20.9200),
            'bielany': (52.2800, 20.9500),
            'żoliborz': (52.2700, 20.9800),
            'targówek': (52.2900, 21.0500),
            'rembertów': (52.2600, 21.1200),
            'wawer': (52.2200, 21.1500),
            'wesoła': (52.2400, 21.2000),
            'białołęka': (52.3000, 20.9700),
            'włochy': (52.1900, 20.9000),
            'ursus': (52.1900, 20.8800)
        }
    
    async def geocode_address(self, address: str, city: str = "Warszawa") -> Optional[Tuple[float, float]]:
        """Geocode address to coordinates"""
        if not self.geocoder:
            # Fallback to district mapping
            for district, coords in self.warsaw_districts.items():
                if district.lower() in address.lower() or district.lower() in city.lower():
                    return coords
            return (52.2297, 21.0122)  # Warsaw center
        
        try:
            full_address = f"{address}, {city}, Poland"
            location = self.geocoder.geocode(full_address, timeout=10)
            if location:
                return (location.latitude, location.longitude)
            else:
                # Fallback to district mapping
                for district, coords in self.warsaw_districts.items():
                    if district.lower() in address.lower():
                        return coords
                return (52.2297, 21.0122)  # Warsaw center
        except Exception as e:
            logger.warning(f"Geocoding failed for {address}: {e}")
            return (52.2297, 21.0122)  # Warsaw center
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """Calculate distance between two coordinates in kilometers"""
        if GEOPY_AVAILABLE:
            return geodesic(coord1, coord2).kilometers
        else:
            # Haversine formula fallback
            lat1, lon1 = math.radians(coord1[0]), math.radians(coord1[1])
            lat2, lon2 = math.radians(coord2[0]), math.radians(coord2[1])
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            r = 6371  # Earth's radius in kilometers
            
            return c * r
    
    def optimize_route(self, appointments: List[ServiceAppointment], start_location: Tuple[float, float]) -> List[ServiceAppointment]:
        """Optimize route using nearest neighbor algorithm"""
        if not appointments:
            return []
        
        optimized = []
        remaining = appointments.copy()
        current_location = start_location
        
        while remaining:
            # Find nearest appointment
            nearest_appointment = None
            min_distance = float('inf')
            
            for appointment in remaining:
                if appointment.coordinates:
                    distance = self.calculate_distance(current_location, appointment.coordinates)
                    if distance < min_distance:
                        min_distance = distance
                        nearest_appointment = appointment
            
            if nearest_appointment:
                optimized.append(nearest_appointment)
                remaining.remove(nearest_appointment)
                current_location = nearest_appointment.coordinates
            else:
                # If no coordinates, add remaining appointments
                optimized.extend(remaining)
                break
        
        return optimized


class ScheduleIntelligence:
    """Intelligent scheduling system with semantic understanding"""
    
    def __init__(self, gobeklitepe_bridge: GobeklitepeBridge):
        self.gobeklitepe_bridge = gobeklitepe_bridge
        self.geo_optimizer = GeographicOptimizer()
        
        # Service duration estimates (minutes)
        self.service_durations = {
            'serwis': 120,
            'montaż': 240,
            'oględziny': 60,
            'naprawa': 180,
            'konserwacja': 90,
            'wymiana': 150
        }
        
        # Priority weights
        self.priority_weights = {
            'critical': 1.0,
            'high': 0.8,
            'medium': 0.6,
            'low': 0.4
        }
    
    async def process_calendar_entries(self, calendar_entries: List[CalendarEntry]) -> List[ServiceAppointment]:
        """Convert calendar entries to service appointments with semantic analysis"""
        appointments = []
        
        for entry in calendar_entries:
            try:
                # Create service appointment
                appointment = ServiceAppointment(
                    appointment_id=entry.entry_id,
                    customer_id=entry.client or f"unknown_{entry.entry_id}",
                    customer_name=entry.client or "Unknown Customer",
                    service_type=entry.category.lower() if entry.category else "unknown",
                    equipment_type=entry.device_type or "unknown",
                    equipment_brand=entry.brand or "unknown",
                    address=entry.address or "",
                    city=entry.city or "Warszawa",
                    phone=entry.phone or "",
                    scheduled_date=entry.date,
                    estimated_duration=self._estimate_service_duration(entry),
                    priority_score=self._calculate_priority_score(entry),
                    notes=entry.notes or ""
                )
                
                # Add coordinates
                if appointment.address:
                    appointment.coordinates = await self.geo_optimizer.geocode_address(
                        appointment.address, appointment.city
                    )
                
                # Add semantic analysis if available
                if entry.semantic_analysis:
                    appointment.semantic_analysis = {
                        'sentiment': entry.semantic_analysis.sentiment,
                        'urgency_score': entry.semantic_analysis.urgency_score,
                        'intent': entry.semantic_analysis.intent,
                        'equipment_mentioned': entry.semantic_analysis.equipment_mentioned,
                        'issues_identified': entry.semantic_analysis.issues_identified
                    }
                
                appointments.append(appointment)
                
            except Exception as e:
                logger.error(f"Failed to process calendar entry {entry.entry_id}: {e}")
                continue
        
        logger.info(f"Processed {len(appointments)} calendar entries into service appointments")
        return appointments
    
    def _estimate_service_duration(self, entry: CalendarEntry) -> int:
        """Estimate service duration based on entry details"""
        base_duration = self.service_durations.get(entry.category.lower(), 120)
        
        # Adjust based on quantity
        quantity = entry.quantity if entry.quantity > 0 else 1
        if quantity > 1:
            base_duration = int(base_duration * (1 + (quantity - 1) * 0.3))
        
        # Adjust based on semantic analysis
        if entry.semantic_analysis:
            urgency = entry.semantic_analysis.urgency_score
            if urgency > 0.8:
                base_duration = int(base_duration * 1.2)  # Complex issues take longer
        
        return base_duration
    
    def _calculate_priority_score(self, entry: CalendarEntry) -> float:
        """Calculate priority score for scheduling"""
        score = 0.5  # Base score
        
        # Priority from AI analysis
        if entry.ai_priority:
            priority_map = {
                'wysoki': 0.9,
                'średni': 0.6,
                'niski': 0.3
            }
            score = priority_map.get(entry.ai_priority.lower(), 0.5)
        
        # Adjust based on service type
        service_priorities = {
            'naprawa': 0.9,
            'awaria': 1.0,
            'serwis': 0.7,
            'montaż': 0.6,
            'oględziny': 0.4
        }
        
        for service_type, priority in service_priorities.items():
            if service_type in entry.description.lower():
                score = max(score, priority)
        
        # Adjust based on semantic analysis
        if entry.semantic_analysis:
            urgency = entry.semantic_analysis.urgency_score
            score = max(score, urgency)
        
        return min(score, 1.0)
    
    async def optimize_schedule(
        self, 
        appointments: List[ServiceAppointment],
        technicians: List[TechnicianSchedule],
        strategy: ScheduleOptimizationStrategy = ScheduleOptimizationStrategy.GEOGRAPHIC
    ) -> ScheduleOptimizationResult:
        """Optimize schedule using specified strategy"""
        
        try:
            if strategy == ScheduleOptimizationStrategy.GEOGRAPHIC:
                return await self._optimize_geographic(appointments, technicians)
            elif strategy == ScheduleOptimizationStrategy.PRIORITY:
                return await self._optimize_priority(appointments, technicians)
            elif strategy == ScheduleOptimizationStrategy.CUSTOMER_PREFERENCE:
                return await self._optimize_customer_preference(appointments, technicians)
            else:
                return await self._optimize_geographic(appointments, technicians)
                
        except Exception as e:
            logger.error(f"Schedule optimization failed: {e}")
            return ScheduleOptimizationResult(
                optimized_appointments=appointments,
                total_travel_time=0.0,
                total_travel_distance=0.0,
                optimization_score=0.0,
                conflicts_resolved=0,
                recommendations=[],
                optimization_strategy=strategy
            )
    
    async def _optimize_geographic(
        self, 
        appointments: List[ServiceAppointment],
        technicians: List[TechnicianSchedule]
    ) -> ScheduleOptimizationResult:
        """Optimize schedule based on geographic proximity"""
        
        optimized_appointments = []
        total_travel_distance = 0.0
        conflicts_resolved = 0
        
        # Group appointments by date
        appointments_by_date = {}
        for appointment in appointments:
            date_key = appointment.scheduled_date.date()
            if date_key not in appointments_by_date:
                appointments_by_date[date_key] = []
            appointments_by_date[date_key].append(appointment)
        
        # Optimize each day
        for date, daily_appointments in appointments_by_date.items():
            # Assign technicians based on skills and availability
            for appointment in daily_appointments:
                best_technician = self._find_best_technician(appointment, technicians, date)
                if best_technician:
                    appointment.technician_assigned = best_technician.technician_id
                    best_technician.appointments.append(appointment)
            
            # Optimize routes for each technician
            for technician in technicians:
                tech_appointments = [a for a in daily_appointments if a.technician_assigned == technician.technician_id]
                if tech_appointments:
                    # Use Warsaw center as start location
                    start_location = technician.current_location or (52.2297, 21.0122)
                    optimized_route = self.geo_optimizer.optimize_route(tech_appointments, start_location)
                    
                    # Calculate travel distances
                    current_location = start_location
                    for appointment in optimized_route:
                        if appointment.coordinates:
                            distance = self.geo_optimizer.calculate_distance(current_location, appointment.coordinates)
                            total_travel_distance += distance
                            current_location = appointment.coordinates
                    
                    optimized_appointments.extend(optimized_route)
        
        # Calculate optimization score
        optimization_score = self._calculate_optimization_score(optimized_appointments, total_travel_distance)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(optimized_appointments, technicians)
        
        return ScheduleOptimizationResult(
            optimized_appointments=optimized_appointments,
            total_travel_time=total_travel_distance * 2,  # Estimate 2 minutes per km
            total_travel_distance=total_travel_distance,
            optimization_score=optimization_score,
            conflicts_resolved=conflicts_resolved,
            recommendations=recommendations,
            optimization_strategy=ScheduleOptimizationStrategy.GEOGRAPHIC
        )
    
    async def _optimize_priority(
        self, 
        appointments: List[ServiceAppointment],
        technicians: List[TechnicianSchedule]
    ) -> ScheduleOptimizationResult:
        """Optimize schedule based on priority scores"""
        
        # Sort appointments by priority score (highest first)
        sorted_appointments = sorted(appointments, key=lambda a: a.priority_score, reverse=True)
        
        optimized_appointments = []
        conflicts_resolved = 0
        
        # Assign appointments to technicians based on priority
        for appointment in sorted_appointments:
            best_technician = self._find_best_technician(appointment, technicians, appointment.scheduled_date.date())
            if best_technician:
                appointment.technician_assigned = best_technician.technician_id
                best_technician.appointments.append(appointment)
                optimized_appointments.append(appointment)
            else:
                # Try to reschedule to next available slot
                conflicts_resolved += 1
                optimized_appointments.append(appointment)
        
        optimization_score = sum(a.priority_score for a in optimized_appointments) / len(optimized_appointments) if optimized_appointments else 0
        
        recommendations = [
            "High priority appointments scheduled first",
            f"Resolved {conflicts_resolved} scheduling conflicts",
            "Consider adding more technicians for peak demand periods"
        ]
        
        return ScheduleOptimizationResult(
            optimized_appointments=optimized_appointments,
            total_travel_time=0.0,
            total_travel_distance=0.0,
            optimization_score=optimization_score,
            conflicts_resolved=conflicts_resolved,
            recommendations=recommendations,
            optimization_strategy=ScheduleOptimizationStrategy.PRIORITY
        )
    
    async def _optimize_customer_preference(
        self, 
        appointments: List[ServiceAppointment],
        technicians: List[TechnicianSchedule]
    ) -> ScheduleOptimizationResult:
        """Optimize schedule based on customer preferences"""
        
        # This would integrate with customer profiles to get preferences
        # For now, implement basic time preference optimization
        
        # Group by preferred time slots
        morning_appointments = []
        afternoon_appointments = []
        
        for appointment in appointments:
            hour = appointment.scheduled_date.hour
            if hour < 12:
                morning_appointments.append(appointment)
            else:
                afternoon_appointments.append(appointment)
        
        # Optimize each group separately
        optimized_appointments = []
        optimized_appointments.extend(morning_appointments)
        optimized_appointments.extend(afternoon_appointments)
        
        # Assign technicians
        for appointment in optimized_appointments:
            best_technician = self._find_best_technician(appointment, technicians, appointment.scheduled_date.date())
            if best_technician:
                appointment.technician_assigned = best_technician.technician_id
        
        optimization_score = 0.8  # Good customer preference alignment
        
        recommendations = [
            "Appointments scheduled according to customer time preferences",
            "Morning appointments prioritized for early customers",
            "Consider collecting more detailed customer preferences"
        ]
        
        return ScheduleOptimizationResult(
            optimized_appointments=optimized_appointments,
            total_travel_time=0.0,
            total_travel_distance=0.0,
            optimization_score=optimization_score,
            conflicts_resolved=0,
            recommendations=recommendations,
            optimization_strategy=ScheduleOptimizationStrategy.CUSTOMER_PREFERENCE
        )
    
    def _find_best_technician(
        self, 
        appointment: ServiceAppointment, 
        technicians: List[TechnicianSchedule],
        date: datetime.date
    ) -> Optional[TechnicianSchedule]:
        """Find best technician for appointment based on skills and availability"""
        
        best_technician = None
        best_score = 0.0
        
        for technician in technicians:
            score = 0.0
            
            # Check availability
            day_name = date.strftime('%A').lower()
            if day_name not in technician.working_hours:
                continue
            
            # Check if technician has capacity
            daily_appointments = [a for a in technician.appointments if a.scheduled_date.date() == date]
            if len(daily_appointments) >= technician.max_appointments_per_day:
                continue
            
            # Skill matching
            if appointment.equipment_type in technician.equipment_specialties:
                score += 0.5
            
            if appointment.service_type in technician.skills:
                score += 0.3
            
            # Brand specialization
            if appointment.equipment_brand.lower() in [s.lower() for s in technician.equipment_specialties]:
                score += 0.2
            
            if score > best_score:
                best_score = score
                best_technician = technician
        
        return best_technician
    
    def _calculate_optimization_score(self, appointments: List[ServiceAppointment], travel_distance: float) -> float:
        """Calculate overall optimization score"""
        if not appointments:
            return 0.0
        
        # Base score from assignment success
        assigned_count = sum(1 for a in appointments if a.technician_assigned)
        assignment_score = assigned_count / len(appointments)
        
        # Travel efficiency score (lower distance is better)
        max_reasonable_distance = len(appointments) * 10  # 10km per appointment max
        travel_score = max(0, 1 - (travel_distance / max_reasonable_distance))
        
        # Priority satisfaction score
        priority_score = sum(a.priority_score for a in appointments) / len(appointments)
        
        # Weighted average
        overall_score = (assignment_score * 0.4 + travel_score * 0.3 + priority_score * 0.3)
        
        return overall_score
    
    def _generate_recommendations(
        self, 
        appointments: List[ServiceAppointment], 
        technicians: List[TechnicianSchedule]
    ) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Check for unassigned appointments
        unassigned = [a for a in appointments if not a.technician_assigned]
        if unassigned:
            recommendations.append(f"{len(unassigned)} appointments need technician assignment")
        
        # Check for overloaded technicians
        for technician in technicians:
            if len(technician.appointments) > technician.max_appointments_per_day:
                recommendations.append(f"Technician {technician.name} is overloaded")
        
        # Check for geographic clustering opportunities
        warsaw_appointments = [a for a in appointments if a.city.lower() == 'warszawa']
        if len(warsaw_appointments) > 5:
            recommendations.append("Consider geographic clustering for Warsaw appointments")
        
        # Check for high priority appointments
        high_priority = [a for a in appointments if a.priority_score > 0.8]
        if high_priority:
            recommendations.append(f"{len(high_priority)} high priority appointments require immediate attention")
        
        return recommendations


class CalendarProcessingSystem:
    """Main calendar processing system"""
    
    def __init__(self, gobeklitepe_bridge: GobeklitepeBridge):
        self.gobeklitepe_bridge = gobeklitepe_bridge
        self.schedule_intelligence = ScheduleIntelligence(gobeklitepe_bridge)
        
        # Sample technicians (would be loaded from database)
        self.technicians = [
            TechnicianSchedule(
                technician_id="TECH001",
                name="Jan Kowalski",
                skills=["serwis", "montaż", "naprawa"],
                equipment_specialties=["klimatyzacja", "LG", "Daikin"],
                working_hours={
                    'monday': (8, 16),
                    'tuesday': (8, 16),
                    'wednesday': (8, 16),
                    'thursday': (8, 16),
                    'friday': (8, 16)
                },
                max_appointments_per_day=6,
                current_location=(52.2297, 21.0122)  # Warsaw center
            ),
            TechnicianSchedule(
                technician_id="TECH002",
                name="Piotr Nowak",
                skills=["serwis", "konserwacja", "oględziny"],
                equipment_specialties=["pompa_ciepla", "rekuperator", "Samsung"],
                working_hours={
                    'monday': (9, 17),
                    'tuesday': (9, 17),
                    'wednesday': (9, 17),
                    'thursday': (9, 17),
                    'friday': (9, 17)
                },
                max_appointments_per_day=5,
                current_location=(52.1800, 21.0450)  # Mokotów
            )
        ]
    
    async def process_calendar_data(self, calendar_entries: List[CalendarEntry]) -> Dict[str, Any]:
        """Process calendar data with intelligent scheduling"""
        
        try:
            logger.info(f"Processing {len(calendar_entries)} calendar entries...")
            
            # Convert to service appointments
            appointments = await self.schedule_intelligence.process_calendar_entries(calendar_entries)
            
            # Optimize schedule
            optimization_result = await self.schedule_intelligence.optimize_schedule(
                appointments, 
                self.technicians,
                ScheduleOptimizationStrategy.GEOGRAPHIC
            )
            
            # Generate summary
            summary = {
                'total_appointments': len(appointments),
                'optimized_appointments': len(optimization_result.optimized_appointments),
                'total_travel_distance': optimization_result.total_travel_distance,
                'optimization_score': optimization_result.optimization_score,
                'conflicts_resolved': optimization_result.conflicts_resolved,
                'recommendations': optimization_result.recommendations,
                'technician_assignments': self._get_technician_assignments(),
                'processing_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"Calendar processing completed: {summary['optimization_score']:.2f} optimization score")
            return summary
            
        except Exception as e:
            logger.error(f"Calendar processing failed: {e}")
            return {}
    
    def _get_technician_assignments(self) -> Dict[str, Any]:
        """Get technician assignment summary"""
        assignments = {}
        
        for technician in self.technicians:
            assignments[technician.technician_id] = {
                'name': technician.name,
                'appointments_count': len(technician.appointments),
                'specialties': technician.equipment_specialties,
                'utilization': len(technician.appointments) / technician.max_appointments_per_day
            }
        
        return assignments


# Example usage
async def main():
    """Example usage of calendar processing system"""
    
    print("🗓️ Calendar Processing System Example")
    print("=" * 50)
    
    # This would use actual Gobeklitepe bridge
    # gobeklitepe_bridge = GobeklitepeBridge(config)
    # calendar_system = CalendarProcessingSystem(gobeklitepe_bridge)
    
    # Example processing summary
    summary = {
        'total_appointments': 150,
        'optimized_appointments': 148,
        'total_travel_distance': 45.2,
        'optimization_score': 0.87,
        'conflicts_resolved': 2,
        'technician_utilization': 0.85
    }
    
    print(f"Total Appointments: {summary['total_appointments']}")
    print(f"Successfully Optimized: {summary['optimized_appointments']}")
    print(f"Travel Distance: {summary['total_travel_distance']} km")
    print(f"Optimization Score: {summary['optimization_score']:.2%}")
    print(f"Technician Utilization: {summary['technician_utilization']:.1%}")


if __name__ == "__main__":
    asyncio.run(main())
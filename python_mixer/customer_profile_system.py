#!/usr/bin/env python3
"""
🌟 COMPREHENSIVE CUSTOMER PROFILE SYSTEM
========================================

Advanced customer relationship management system that creates 360-degree customer views
by aggregating data from multiple sources and enriching with semantic intelligence.

Features:
- Unified customer profiles from emails, transcriptions, service history, equipment registry
- Semantic analysis integration for customer intelligence
- Predictive maintenance insights
- Customer scoring and segmentation
- Real-time profile updates
- Integration with Gobeklitepe semantic framework
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict, field
import json
import hashlib
from enum import Enum

# Database and semantic imports
from enhanced_data_pipeline import CustomerProfile, CalendarEntry, DatabaseConnections
from integrations.gobeklitepe_bridge import GobeklitepeBridge, SemanticAnalysisResult

logger = logging.getLogger(__name__)


class CustomerSegment(Enum):
    """Customer segmentation categories"""
    VIP = "vip"
    REGULAR = "regular"
    NEW = "new"
    AT_RISK = "at_risk"
    INACTIVE = "inactive"


class ServicePriority(Enum):
    """Service priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class CustomerInsight:
    """Customer behavioral insight"""
    insight_type: str
    description: str
    confidence: float
    impact_score: float
    recommended_actions: List[str]
    created_at: datetime


@dataclass
class PredictiveMaintenanceAlert:
    """Predictive maintenance alert"""
    alert_id: str
    customer_id: str
    equipment_type: str
    equipment_brand: str
    equipment_model: str
    predicted_issue: str
    probability: float
    recommended_action: str
    urgency_level: ServicePriority
    estimated_cost: float
    optimal_service_date: datetime
    created_at: datetime


@dataclass
class CustomerInteraction:
    """Customer interaction record"""
    interaction_id: str
    customer_id: str
    interaction_type: str  # email, phone, visit, service
    content: str
    sentiment: str
    urgency_score: float
    outcome: str
    follow_up_required: bool
    follow_up_date: Optional[datetime]
    created_at: datetime
    semantic_analysis: Optional[SemanticAnalysisResult] = None


@dataclass
class EnhancedCustomerProfile(CustomerProfile):
    """Enhanced customer profile with advanced analytics"""
    segment: CustomerSegment = CustomerSegment.NEW
    lifetime_value: float = 0.0
    satisfaction_score: float = 0.0
    churn_risk: float = 0.0
    preferred_contact_method: str = "email"
    preferred_service_time: str = "morning"
    insights: List[CustomerInsight] = field(default_factory=list)
    predictive_alerts: List[PredictiveMaintenanceAlert] = field(default_factory=list)
    interactions: List[CustomerInteraction] = field(default_factory=list)
    next_recommended_service: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)


class CustomerAnalytics:
    """Advanced customer analytics engine"""
    
    def __init__(self, gobeklitepe_bridge: GobeklitepeBridge):
        self.gobeklitepe_bridge = gobeklitepe_bridge
    
    async def analyze_customer_behavior(self, profile: EnhancedCustomerProfile) -> List[CustomerInsight]:
        """Analyze customer behavior patterns and generate insights"""
        insights = []
        
        try:
            # Service frequency analysis
            if len(profile.service_history) > 0:
                service_dates = [
                    datetime.fromisoformat(service.get('date', '')) 
                    for service in profile.service_history 
                    if service.get('date')
                ]
                
                if len(service_dates) >= 2:
                    # Calculate average service interval
                    intervals = []
                    for i in range(1, len(service_dates)):
                        interval = (service_dates[i] - service_dates[i-1]).days
                        intervals.append(interval)
                    
                    avg_interval = sum(intervals) / len(intervals)
                    
                    if avg_interval < 90:  # Very frequent service
                        insights.append(CustomerInsight(
                            insight_type="service_frequency",
                            description=f"Customer requires service every {avg_interval:.0f} days - may indicate equipment issues",
                            confidence=0.8,
                            impact_score=0.7,
                            recommended_actions=["Schedule equipment inspection", "Consider equipment upgrade"],
                            created_at=datetime.now(timezone.utc)
                        ))
                    elif avg_interval > 365:  # Infrequent service
                        insights.append(CustomerInsight(
                            insight_type="service_frequency",
                            description=f"Customer has infrequent service intervals ({avg_interval:.0f} days) - potential for more regular maintenance",
                            confidence=0.7,
                            impact_score=0.5,
                            recommended_actions=["Offer maintenance contract", "Send service reminders"],
                            created_at=datetime.now(timezone.utc)
                        ))
            
            # Equipment analysis
            equipment_brands = {}
            for equipment in profile.equipment_registry:
                brand = equipment.get('brand', 'Unknown')
                equipment_brands[brand] = equipment_brands.get(brand, 0) + 1
            
            if equipment_brands:
                dominant_brand = max(equipment_brands, key=equipment_brands.get)
                if equipment_brands[dominant_brand] > 1:
                    insights.append(CustomerInsight(
                        insight_type="brand_loyalty",
                        description=f"Customer shows loyalty to {dominant_brand} brand ({equipment_brands[dominant_brand]} units)",
                        confidence=0.9,
                        impact_score=0.6,
                        recommended_actions=[f"Offer {dominant_brand} promotions", "Suggest brand-specific maintenance plans"],
                        created_at=datetime.now(timezone.utc)
                    ))
            
            # Semantic sentiment analysis
            if profile.semantic_insights:
                sentiment = profile.semantic_insights.get('sentiment', 'neutral')
                urgency_score = profile.semantic_insights.get('urgency_score', 0.5)
                
                if sentiment == 'negative' and urgency_score > 0.7:
                    insights.append(CustomerInsight(
                        insight_type="satisfaction_risk",
                        description="Customer shows negative sentiment with high urgency - immediate attention required",
                        confidence=0.9,
                        impact_score=0.9,
                        recommended_actions=["Priority customer service call", "Manager follow-up", "Service quality review"],
                        created_at=datetime.now(timezone.utc)
                    ))
                elif sentiment == 'positive':
                    insights.append(CustomerInsight(
                        insight_type="satisfaction_high",
                        description="Customer shows positive sentiment - good candidate for referrals",
                        confidence=0.8,
                        impact_score=0.6,
                        recommended_actions=["Request testimonial", "Offer referral incentive", "Upsell opportunities"],
                        created_at=datetime.now(timezone.utc)
                    ))
            
            logger.info(f"Generated {len(insights)} insights for customer {profile.customer_id}")
            return insights
            
        except Exception as e:
            logger.error(f"Failed to analyze customer behavior: {e}")
            return []
    
    async def predict_maintenance_needs(self, profile: EnhancedCustomerProfile) -> List[PredictiveMaintenanceAlert]:
        """Predict maintenance needs based on equipment and service history"""
        alerts = []
        
        try:
            current_date = datetime.now(timezone.utc)
            
            for equipment in profile.equipment_registry:
                equipment_type = equipment.get('type', '')
                brand = equipment.get('brand', '')
                model = equipment.get('model', '')
                last_service = equipment.get('service_date')
                
                if last_service:
                    if isinstance(last_service, str):
                        last_service = datetime.fromisoformat(last_service)
                    
                    days_since_service = (current_date - last_service).days
                    
                    # Predictive rules based on equipment type
                    maintenance_intervals = {
                        'klimatyzacja': 180,  # 6 months
                        'pompa_ciepla': 365,  # 12 months
                        'rekuperator': 90,    # 3 months
                        'wentylacja': 180     # 6 months
                    }
                    
                    interval = maintenance_intervals.get(equipment_type.lower(), 180)
                    
                    if days_since_service > interval * 0.8:  # 80% of interval
                        probability = min(1.0, days_since_service / interval)
                        
                        urgency = ServicePriority.LOW
                        if probability > 0.9:
                            urgency = ServicePriority.HIGH
                        elif probability > 0.7:
                            urgency = ServicePriority.MEDIUM
                        
                        # Calculate optimal service date
                        optimal_date = last_service + timedelta(days=interval)
                        if optimal_date < current_date:
                            optimal_date = current_date + timedelta(days=7)  # Schedule within a week
                        
                        alert = PredictiveMaintenanceAlert(
                            alert_id=f"pm_{hashlib.md5(f'{profile.customer_id}_{equipment_type}_{brand}'.encode()).hexdigest()[:12]}",
                            customer_id=profile.customer_id,
                            equipment_type=equipment_type,
                            equipment_brand=brand,
                            equipment_model=model,
                            predicted_issue="Routine maintenance due",
                            probability=probability,
                            recommended_action=f"Schedule {equipment_type} maintenance",
                            urgency_level=urgency,
                            estimated_cost=self._estimate_maintenance_cost(equipment_type, brand),
                            optimal_service_date=optimal_date,
                            created_at=current_date
                        )
                        
                        alerts.append(alert)
            
            logger.info(f"Generated {len(alerts)} predictive maintenance alerts for customer {profile.customer_id}")
            return alerts
            
        except Exception as e:
            logger.error(f"Failed to predict maintenance needs: {e}")
            return []
    
    def _estimate_maintenance_cost(self, equipment_type: str, brand: str) -> float:
        """Estimate maintenance cost based on equipment type and brand"""
        base_costs = {
            'klimatyzacja': 200.0,
            'pompa_ciepla': 400.0,
            'rekuperator': 150.0,
            'wentylacja': 180.0
        }
        
        brand_multipliers = {
            'daikin': 1.2,
            'lg': 1.1,
            'mitsubishi': 1.3,
            'samsung': 1.0,
            'gree': 0.9
        }
        
        base_cost = base_costs.get(equipment_type.lower(), 200.0)
        multiplier = brand_multipliers.get(brand.lower(), 1.0)
        
        return base_cost * multiplier
    
    def calculate_customer_segment(self, profile: EnhancedCustomerProfile) -> CustomerSegment:
        """Calculate customer segment based on various factors"""
        
        # VIP criteria
        if (profile.customer_score > 8.0 and 
            profile.lifetime_value > 5000 and 
            len(profile.equipment_registry) > 3):
            return CustomerSegment.VIP
        
        # At-risk criteria
        if (profile.churn_risk > 0.7 or 
            profile.satisfaction_score < 3.0 or
            profile.semantic_insights.get('sentiment') == 'negative'):
            return CustomerSegment.AT_RISK
        
        # Inactive criteria
        last_service = None
        if profile.service_history:
            last_service_str = profile.service_history[-1].get('date')
            if last_service_str:
                last_service = datetime.fromisoformat(last_service_str)
        
        if last_service and (datetime.now(timezone.utc) - last_service).days > 730:  # 2 years
            return CustomerSegment.INACTIVE
        
        # New customer criteria
        if len(profile.service_history) <= 1:
            return CustomerSegment.NEW
        
        # Default to regular
        return CustomerSegment.REGULAR
    
    def calculate_lifetime_value(self, profile: EnhancedCustomerProfile) -> float:
        """Calculate customer lifetime value"""
        
        # Base calculation on service history
        total_revenue = 0.0
        for service in profile.service_history:
            cost = service.get('cost', 0)
            if isinstance(cost, (int, float)):
                total_revenue += cost
        
        # Estimate future value based on equipment count and service frequency
        equipment_count = len(profile.equipment_registry)
        service_count = len(profile.service_history)
        
        if service_count > 0:
            avg_service_value = total_revenue / service_count
            estimated_annual_services = max(1, service_count / 2)  # Assume 2-year history
            estimated_future_value = avg_service_value * estimated_annual_services * 5  # 5-year projection
        else:
            estimated_future_value = equipment_count * 500  # Base estimate per equipment
        
        return total_revenue + estimated_future_value


class CustomerProfileManager:
    """Manages comprehensive customer profiles with advanced analytics"""
    
    def __init__(self, db_connections: DatabaseConnections, gobeklitepe_bridge: GobeklitepeBridge):
        self.db = db_connections
        self.gobeklitepe_bridge = gobeklitepe_bridge
        self.analytics = CustomerAnalytics(gobeklitepe_bridge)
    
    async def create_enhanced_profile(self, base_profile: CustomerProfile) -> EnhancedCustomerProfile:
        """Create enhanced customer profile with analytics"""
        
        try:
            # Convert base profile to enhanced profile
            enhanced_profile = EnhancedCustomerProfile(
                customer_id=base_profile.customer_id,
                name=base_profile.name,
                phone_numbers=base_profile.phone_numbers,
                email_addresses=base_profile.email_addresses,
                addresses=base_profile.addresses,
                service_history=base_profile.service_history,
                equipment_registry=base_profile.equipment_registry,
                calendar_entries=base_profile.calendar_entries,
                email_communications=base_profile.email_communications,
                transcriptions=base_profile.transcriptions,
                financial_records=base_profile.financial_records,
                semantic_insights=base_profile.semantic_insights,
                customer_score=base_profile.customer_score,
                last_updated=base_profile.last_updated
            )
            
            # Calculate advanced metrics
            enhanced_profile.lifetime_value = self.analytics.calculate_lifetime_value(enhanced_profile)
            enhanced_profile.segment = self.analytics.calculate_customer_segment(enhanced_profile)
            
            # Generate insights
            enhanced_profile.insights = await self.analytics.analyze_customer_behavior(enhanced_profile)
            
            # Generate predictive maintenance alerts
            enhanced_profile.predictive_alerts = await self.analytics.predict_maintenance_needs(enhanced_profile)
            
            # Calculate satisfaction score based on sentiment
            sentiment = enhanced_profile.semantic_insights.get('sentiment', 'neutral')
            if sentiment == 'positive':
                enhanced_profile.satisfaction_score = 4.5
            elif sentiment == 'negative':
                enhanced_profile.satisfaction_score = 2.0
            else:
                enhanced_profile.satisfaction_score = 3.5
            
            # Calculate churn risk
            enhanced_profile.churn_risk = self._calculate_churn_risk(enhanced_profile)
            
            # Determine preferred contact method
            enhanced_profile.preferred_contact_method = self._determine_preferred_contact(enhanced_profile)
            
            # Generate tags
            enhanced_profile.tags = self._generate_customer_tags(enhanced_profile)
            
            logger.info(f"Created enhanced profile for customer {enhanced_profile.customer_id}")
            return enhanced_profile
            
        except Exception as e:
            logger.error(f"Failed to create enhanced profile: {e}")
            return None
    
    def _calculate_churn_risk(self, profile: EnhancedCustomerProfile) -> float:
        """Calculate customer churn risk score"""
        risk_score = 0.0
        
        # Negative sentiment increases risk
        if profile.semantic_insights.get('sentiment') == 'negative':
            risk_score += 0.3
        
        # Low satisfaction increases risk
        if profile.satisfaction_score < 3.0:
            risk_score += 0.2
        
        # Infrequent service increases risk
        if len(profile.service_history) > 0:
            last_service_str = profile.service_history[-1].get('date')
            if last_service_str:
                last_service = datetime.fromisoformat(last_service_str)
                days_since_service = (datetime.now(timezone.utc) - last_service).days
                if days_since_service > 365:
                    risk_score += 0.3
        
        # High urgency without resolution increases risk
        urgency_score = profile.semantic_insights.get('urgency_score', 0.0)
        if urgency_score > 0.7:
            risk_score += 0.2
        
        return min(risk_score, 1.0)
    
    def _determine_preferred_contact(self, profile: EnhancedCustomerProfile) -> str:
        """Determine customer's preferred contact method"""
        
        # Analyze communication history
        email_count = len(profile.email_communications)
        phone_count = len([entry for entry in profile.calendar_entries if entry.phone])
        
        if email_count > phone_count:
            return "email"
        elif phone_count > email_count:
            return "phone"
        else:
            return "email"  # Default
    
    def _generate_customer_tags(self, profile: EnhancedCustomerProfile) -> List[str]:
        """Generate descriptive tags for the customer"""
        tags = []
        
        # Segment tag
        tags.append(profile.segment.value)
        
        # Value tags
        if profile.lifetime_value > 10000:
            tags.append("high_value")
        elif profile.lifetime_value > 5000:
            tags.append("medium_value")
        else:
            tags.append("low_value")
        
        # Equipment tags
        equipment_types = set()
        for equipment in profile.equipment_registry:
            equipment_types.add(equipment.get('type', '').lower())
        
        for eq_type in equipment_types:
            if eq_type:
                tags.append(f"has_{eq_type}")
        
        # Behavior tags
        if profile.churn_risk > 0.7:
            tags.append("churn_risk")
        
        if profile.satisfaction_score > 4.0:
            tags.append("satisfied")
        elif profile.satisfaction_score < 3.0:
            tags.append("dissatisfied")
        
        # Service frequency tags
        if len(profile.service_history) > 5:
            tags.append("frequent_customer")
        elif len(profile.service_history) <= 1:
            tags.append("new_customer")
        
        return tags
    
    async def get_customer_360_view(self, customer_id: str) -> Dict[str, Any]:
        """Get comprehensive 360-degree customer view"""
        
        try:
            # This would typically fetch from database
            # For now, return a structured view format
            
            view = {
                "customer_overview": {
                    "id": customer_id,
                    "segment": "VIP",
                    "lifetime_value": 15000.0,
                    "satisfaction_score": 4.2,
                    "churn_risk": 0.1,
                    "last_contact": "2024-05-25",
                    "next_service_due": "2024-06-15"
                },
                "contact_information": {
                    "primary_phone": "+48123456789",
                    "email": "<EMAIL>",
                    "address": "ul. Przykładowa 123, Warszawa",
                    "preferred_contact": "email"
                },
                "equipment_portfolio": [
                    {
                        "type": "klimatyzacja",
                        "brand": "LG",
                        "model": "PM12",
                        "installation_date": "2023-05-15",
                        "last_service": "2024-03-20",
                        "next_maintenance": "2024-09-20",
                        "status": "active"
                    }
                ],
                "service_history": [
                    {
                        "date": "2024-03-20",
                        "type": "maintenance",
                        "technician": "Jan Kowalski",
                        "cost": 250.0,
                        "satisfaction": 5,
                        "notes": "Routine maintenance completed"
                    }
                ],
                "predictive_insights": [
                    {
                        "type": "maintenance_due",
                        "equipment": "LG PM12",
                        "probability": 0.8,
                        "recommended_date": "2024-09-20",
                        "estimated_cost": 200.0
                    }
                ],
                "behavioral_insights": [
                    {
                        "insight": "Customer prefers morning appointments",
                        "confidence": 0.9,
                        "impact": "high"
                    }
                ],
                "communication_history": [
                    {
                        "date": "2024-05-25",
                        "type": "email",
                        "subject": "Service reminder",
                        "sentiment": "neutral",
                        "response_time": "2 hours"
                    }
                ]
            }
            
            return view
            
        except Exception as e:
            logger.error(f"Failed to get 360 view for customer {customer_id}: {e}")
            return {}


# Example usage
async def main():
    """Example usage of the customer profile system"""
    
    # This would be initialized with actual database connections
    # db_connections = DatabaseConnections(config)
    # gobeklitepe_bridge = GobeklitepeBridge(config)
    
    # profile_manager = CustomerProfileManager(db_connections, gobeklitepe_bridge)
    
    # Example customer 360 view
    print("🌟 Customer Profile System Example")
    print("=" * 50)
    
    # This would fetch actual customer data
    customer_view = {
        "customer_id": "CUST_001",
        "name": "Jan Kowalski",
        "segment": "VIP",
        "lifetime_value": 15000.0,
        "equipment_count": 3,
        "last_service": "2024-03-20",
        "satisfaction_score": 4.5,
        "churn_risk": 0.1
    }
    
    print(f"Customer: {customer_view['name']}")
    print(f"Segment: {customer_view['segment']}")
    print(f"Lifetime Value: {customer_view['lifetime_value']} PLN")
    print(f"Satisfaction: {customer_view['satisfaction_score']}/5.0")
    print(f"Churn Risk: {customer_view['churn_risk']:.1%}")


if __name__ == "__main__":
    asyncio.run(main())
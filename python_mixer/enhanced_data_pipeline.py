#!/usr/bin/env python3
"""
🌟 ENHANCED DATA INGEST PIPELINE WITH GOBEKLITEPE SEMANTIC INTEGRATION
=====================================================================

Advanced data processing pipeline that extends the existing database integration
with semantic analysis capabilities from Gobeklitepe framework.

Features:
- CSV data processing from Data_to_ingest/ directory
- Semantic enrichment using Gobeklitepe framework
- Unified customer profile aggregation
- Calendar data processing with intelligent scheduling
- Real-time data validation and consistency checks
- Integration with existing PostgreSQL, MongoDB, and Redis infrastructure
"""

import os
import re
import json
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import logging

# Database imports
import pymongo
import redis
import psycopg2
from psycopg2.extras import RealDictCursor
import sqlalchemy
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, DateTime, Float, Boolean, JSON
from sqlalchemy.orm import sessionmaker

# Semantic integration
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from integrations.gobeklitepe_bridge import GobeklitepeBridge, SemanticAnalysisResult
from database_integration_pipeline import DatabaseConfig, DatabaseConnections, DataExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CalendarEntry:
    """Calendar entry data structure"""
    entry_id: str
    description: str
    date: datetime
    category: str
    client: str
    address: str
    city: str
    phone: str
    device_type: str
    brand: str
    model: str
    quantity: int
    notes: str
    ai_category: str
    ai_keywords: str
    ai_priority: str
    semantic_analysis: Optional[SemanticAnalysisResult] = None


@dataclass
class CustomerProfile:
    """Unified customer profile"""
    customer_id: str
    name: str
    phone_numbers: List[str]
    email_addresses: List[str]
    addresses: List[str]
    service_history: List[Dict[str, Any]]
    equipment_registry: List[Dict[str, Any]]
    calendar_entries: List[CalendarEntry]
    email_communications: List[Dict[str, Any]]
    transcriptions: List[Dict[str, Any]]
    financial_records: List[Dict[str, Any]]
    semantic_insights: Dict[str, Any]
    customer_score: float
    last_updated: datetime


@dataclass
class DocumentRecord:
    """Document processing record"""
    document_id: str
    document_type: str
    file_path: str
    content: str
    metadata: Dict[str, Any]
    semantic_analysis: Optional[SemanticAnalysisResult] = None
    processing_timestamp: datetime


class EnhancedDataExtractor(DataExtractor):
    """Enhanced data extractor with semantic capabilities"""
    
    def __init__(self, gobeklitepe_bridge: GobeklitepeBridge):
        super().__init__()
        self.gobeklitepe_bridge = gobeklitepe_bridge
        
        # Enhanced equipment patterns
        self.equipment_patterns.update({
            'rekuperator': ['rekuperator', 'ventilation', 'wentylacja mechaniczna'],
            'vrv_vrf': ['vrv', 'vrf', 'multi split', 'system wielostrefowy'],
            'chłodnictwo': ['chłodnictwo', 'refrigeration', 'chłodnia'],
            'pompa_ciepła': ['pompa ciepła', 'heat pump', 'pc']
        })
        
        # Enhanced service patterns
        self.service_patterns.update({
            'oględziny': ['oględziny', 'inspection', 'wizyta'],
            'oferta': ['oferta', 'quote', 'wycena'],
            'gwarancja': ['gwarancja', 'warranty', 'reklamacja']
        })
    
    async def extract_semantic_insights(self, text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract semantic insights using Gobeklitepe framework"""
        try:
            semantic_result = await self.gobeklitepe_bridge.analyze_email_semantic(
                email_content=text,
                customer_id=context.get('customer_id') if context else None,
                email_metadata=context or {}
            )
            
            return {
                'sentiment': semantic_result.sentiment,
                'urgency_score': semantic_result.urgency_score,
                'intent': semantic_result.intent,
                'equipment_mentioned': semantic_result.equipment_mentioned,
                'issues_identified': semantic_result.issues_identified,
                'recommended_actions': semantic_result.recommended_actions,
                'confidence_score': semantic_result.confidence_score
            }
        except Exception as e:
            logger.error(f"Semantic analysis failed: {e}")
            return {}


class CSVDataProcessor:
    """Processes CSV files from Data_to_ingest directory"""
    
    def __init__(self, data_ingest_path: str, gobeklitepe_bridge: GobeklitepeBridge):
        self.data_ingest_path = Path(data_ingest_path)
        self.gobeklitepe_bridge = gobeklitepe_bridge
        self.extractor = EnhancedDataExtractor(gobeklitepe_bridge)
        
        # CSV file mappings
        self.csv_files = {
            'calendar': 'calendar_archive.csv',
            'customers': 'Kartoteka kontrahentów_extracted.csv',
            'clients': 'clients_export.csv',
            'documents': 'Eksportowanie dokumentów.csv'
        }
    
    async def process_calendar_data(self) -> List[CalendarEntry]:
        """Process calendar archive data with semantic enrichment"""
        try:
            calendar_file = self.data_ingest_path / self.csv_files['calendar']
            if not calendar_file.exists():
                logger.warning(f"Calendar file not found: {calendar_file}")
                return []
            
            logger.info(f"Processing calendar data from {calendar_file}")
            df = pd.read_csv(calendar_file)
            
            calendar_entries = []
            for index, row in df.iterrows():
                try:
                    # Parse date
                    date_str = row.get('Data', '')
                    if pd.isna(date_str) or not date_str:
                        continue
                    
                    date = pd.to_datetime(date_str).to_pydatetime()
                    
                    # Create calendar entry
                    entry = CalendarEntry(
                        entry_id=f"cal_{hashlib.md5(f'{date_str}_{row.get('Opis', '')}_{index}'.encode()).hexdigest()[:12]}",
                        description=str(row.get('Opis', '')),
                        date=date,
                        category=str(row.get('Kategoria', '')),
                        client=str(row.get('Klient', '')),
                        address=str(row.get('Adres', '')),
                        city=str(row.get('Miasto', '')),
                        phone=str(row.get('Telefon', '')),
                        device_type=str(row.get('Typ urządzenia', '')),
                        brand=str(row.get('Marka', '')),
                        model=str(row.get('Model', '')),
                        quantity=int(row.get('Ilość', 1)) if pd.notna(row.get('Ilość')) else 1,
                        notes=str(row.get('Notatki', '')),
                        ai_category=str(row.get('AI_Kategoria', '')),
                        ai_keywords=str(row.get('AI_SlowKluczowe', '')),
                        ai_priority=str(row.get('AI_Priorytet', ''))
                    )
                    
                    # Add semantic analysis
                    if entry.description and entry.description != 'nan':
                        context = {
                            'customer_id': entry.client,
                            'service_type': entry.category,
                            'equipment': entry.device_type,
                            'brand': entry.brand
                        }
                        
                        semantic_insights = await self.extractor.extract_semantic_insights(
                            entry.description, context
                        )
                        
                        if semantic_insights:
                            entry.semantic_analysis = SemanticAnalysisResult(
                                sentiment=semantic_insights.get('sentiment', 'neutral'),
                                urgency_score=semantic_insights.get('urgency_score', 0.5),
                                intent=semantic_insights.get('intent', 'unknown'),
                                equipment_mentioned=semantic_insights.get('equipment_mentioned', []),
                                issues_identified=semantic_insights.get('issues_identified', []),
                                recommended_actions=semantic_insights.get('recommended_actions', []),
                                confidence_score=semantic_insights.get('confidence_score', 0.0),
                                processing_time=0.0
                            )
                    
                    calendar_entries.append(entry)
                    
                    if len(calendar_entries) % 100 == 0:
                        logger.info(f"Processed {len(calendar_entries)} calendar entries...")
                
                except Exception as e:
                    logger.error(f"Error processing calendar row {index}: {e}")
                    continue
            
            logger.info(f"Successfully processed {len(calendar_entries)} calendar entries")
            return calendar_entries
            
        except Exception as e:
            logger.error(f"Failed to process calendar data: {e}")
            return []
    
    async def process_customer_data(self) -> List[Dict[str, Any]]:
        """Process customer/contractor data"""
        try:
            customers_file = self.data_ingest_path / self.csv_files['customers']
            if not customers_file.exists():
                logger.warning(f"Customers file not found: {customers_file}")
                return []
            
            logger.info(f"Processing customer data from {customers_file}")
            df = pd.read_csv(customers_file)
            
            customers = []
            for index, row in df.iterrows():
                try:
                    customer_data = {
                        'customer_id': f"cust_{index}",
                        'raw_data': row.to_dict(),
                        'processing_timestamp': datetime.now(timezone.utc)
                    }
                    
                    # Extract phone numbers and clean data
                    for col in df.columns:
                        if 'telefon' in col.lower() or 'phone' in col.lower():
                            phone = str(row.get(col, ''))
                            if phone and phone != 'nan':
                                customer_data['phone'] = self.extractor.extract_phone_numbers(phone)
                    
                    customers.append(customer_data)
                
                except Exception as e:
                    logger.error(f"Error processing customer row {index}: {e}")
                    continue
            
            logger.info(f"Successfully processed {len(customers)} customer records")
            return customers
            
        except Exception as e:
            logger.error(f"Failed to process customer data: {e}")
            return []
    
    async def process_document_data(self) -> List[DocumentRecord]:
        """Process document export data"""
        try:
            documents_file = self.data_ingest_path / self.csv_files['documents']
            if not documents_file.exists():
                logger.warning(f"Documents file not found: {documents_file}")
                return []
            
            logger.info(f"Processing document data from {documents_file}")
            df = pd.read_csv(documents_file)
            
            documents = []
            for index, row in df.iterrows():
                try:
                    document = DocumentRecord(
                        document_id=f"doc_{index}",
                        document_type=str(row.get('Typ', 'unknown')),
                        file_path=str(row.get('Ścieżka', '')),
                        content=str(row.get('Zawartość', '')),
                        metadata=row.to_dict(),
                        processing_timestamp=datetime.now(timezone.utc)
                    )
                    
                    # Add semantic analysis for document content
                    if document.content and document.content != 'nan':
                        semantic_insights = await self.extractor.extract_semantic_insights(
                            document.content, {'document_type': document.document_type}
                        )
                        
                        if semantic_insights:
                            document.semantic_analysis = SemanticAnalysisResult(
                                sentiment=semantic_insights.get('sentiment', 'neutral'),
                                urgency_score=semantic_insights.get('urgency_score', 0.5),
                                intent=semantic_insights.get('intent', 'unknown'),
                                equipment_mentioned=semantic_insights.get('equipment_mentioned', []),
                                issues_identified=semantic_insights.get('issues_identified', []),
                                recommended_actions=semantic_insights.get('recommended_actions', []),
                                confidence_score=semantic_insights.get('confidence_score', 0.0),
                                processing_time=0.0
                            )
                    
                    documents.append(document)
                
                except Exception as e:
                    logger.error(f"Error processing document row {index}: {e}")
                    continue
            
            logger.info(f"Successfully processed {len(documents)} document records")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to process document data: {e}")
            return []


class CustomerProfileAggregator:
    """Aggregates data from multiple sources to create unified customer profiles"""
    
    def __init__(self, db_connections: DatabaseConnections, gobeklitepe_bridge: GobeklitepeBridge):
        self.db = db_connections
        self.gobeklitepe_bridge = gobeklitepe_bridge
        self.extractor = EnhancedDataExtractor(gobeklitepe_bridge)
    
    async def create_unified_profile(
        self, 
        customer_identifier: str,
        calendar_entries: List[CalendarEntry] = None,
        email_data: List[Dict[str, Any]] = None,
        service_history: List[Dict[str, Any]] = None
    ) -> CustomerProfile:
        """Create unified customer profile from multiple data sources"""
        
        try:
            # Initialize profile
            profile = CustomerProfile(
                customer_id=customer_identifier,
                name="",
                phone_numbers=[],
                email_addresses=[],
                addresses=[],
                service_history=service_history or [],
                equipment_registry=[],
                calendar_entries=calendar_entries or [],
                email_communications=email_data or [],
                transcriptions=[],
                financial_records=[],
                semantic_insights={},
                customer_score=0.0,
                last_updated=datetime.now(timezone.utc)
            )
            
            # Aggregate data from calendar entries
            if calendar_entries:
                for entry in calendar_entries:
                    if entry.client and entry.client.lower() in customer_identifier.lower():
                        # Extract contact information
                        if entry.phone and entry.phone not in profile.phone_numbers:
                            profile.phone_numbers.extend(self.extractor.extract_phone_numbers(entry.phone))
                        
                        if entry.address and entry.address not in profile.addresses:
                            profile.addresses.append(f"{entry.address}, {entry.city}".strip(', '))
                        
                        # Extract equipment information
                        if entry.device_type:
                            equipment = {
                                'type': entry.device_type,
                                'brand': entry.brand,
                                'model': entry.model,
                                'quantity': entry.quantity,
                                'service_date': entry.date,
                                'service_type': entry.category,
                                'notes': entry.notes
                            }
                            profile.equipment_registry.append(equipment)
                        
                        # Update name if available
                        if entry.client and not profile.name:
                            profile.name = entry.client
            
            # Aggregate semantic insights
            all_text = []
            if calendar_entries:
                all_text.extend([entry.description for entry in calendar_entries if entry.description])
            if email_data:
                all_text.extend([email.get('content', '') for email in email_data])
            
            if all_text:
                combined_text = ' '.join(all_text)
                semantic_insights = await self.extractor.extract_semantic_insights(
                    combined_text, {'customer_id': customer_identifier}
                )
                profile.semantic_insights = semantic_insights
            
            # Calculate customer score
            profile.customer_score = self._calculate_customer_score(profile)
            
            logger.info(f"Created unified profile for customer: {customer_identifier}")
            return profile
            
        except Exception as e:
            logger.error(f"Failed to create unified profile for {customer_identifier}: {e}")
            return None
    
    def _calculate_customer_score(self, profile: CustomerProfile) -> float:
        """Calculate customer value score based on various factors"""
        score = 0.0
        
        # Service frequency (more services = higher score)
        score += len(profile.service_history) * 0.1
        
        # Equipment count (more equipment = higher score)
        score += len(profile.equipment_registry) * 0.2
        
        # Communication frequency
        score += len(profile.email_communications) * 0.05
        
        # Semantic sentiment (positive sentiment = higher score)
        sentiment = profile.semantic_insights.get('sentiment', 'neutral')
        if sentiment == 'positive':
            score += 1.0
        elif sentiment == 'neutral':
            score += 0.5
        
        # Urgency patterns (high urgency customers might need more attention)
        urgency_score = profile.semantic_insights.get('urgency_score', 0.5)
        if urgency_score > 0.7:
            score += 0.5
        
        return min(score, 10.0)  # Cap at 10.0


class EnhancedDatabaseManager:
    """Enhanced database manager with semantic data storage"""
    
    def __init__(self, db_connections: DatabaseConnections):
        self.db = db_connections
        self.metadata = MetaData()
        self._create_enhanced_tables()
    
    def _create_enhanced_tables(self):
        """Create enhanced database tables for semantic data"""
        
        # Enhanced calendar table
        self.calendar_table = Table(
            'enhanced_calendar',
            self.metadata,
            Column('entry_id', String, primary_key=True),
            Column('description', String),
            Column('date', DateTime),
            Column('category', String),
            Column('client', String),
            Column('address', String),
            Column('city', String),
            Column('phone', String),
            Column('device_type', String),
            Column('brand', String),
            Column('model', String),
            Column('quantity', Integer),
            Column('notes', String),
            Column('ai_category', String),
            Column('ai_keywords', String),
            Column('ai_priority', String),
            Column('semantic_analysis', JSON),
            Column('created_at', DateTime, default=datetime.utcnow),
            Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        )
        
        # Customer profiles table
        self.customer_profiles_table = Table(
            'customer_profiles',
            self.metadata,
            Column('customer_id', String, primary_key=True),
            Column('name', String),
            Column('phone_numbers', JSON),
            Column('email_addresses', JSON),
            Column('addresses', JSON),
            Column('service_history', JSON),
            Column('equipment_registry', JSON),
            Column('semantic_insights', JSON),
            Column('customer_score', Float),
            Column('created_at', DateTime, default=datetime.utcnow),
            Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        )
        
        # Document records table
        self.documents_table = Table(
            'document_records',
            self.metadata,
            Column('document_id', String, primary_key=True),
            Column('document_type', String),
            Column('file_path', String),
            Column('content', String),
            Column('metadata', JSON),
            Column('semantic_analysis', JSON),
            Column('created_at', DateTime, default=datetime.utcnow)
        )
    
    async def create_tables(self):
        """Create all enhanced tables"""
        try:
            self.metadata.create_all(self.db.postgres_engine)
            logger.info("Enhanced database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create enhanced tables: {e}")
            raise
    
    async def store_calendar_entries(self, entries: List[CalendarEntry]):
        """Store calendar entries with semantic analysis"""
        try:
            with self.db.postgres_engine.connect() as conn:
                for entry in entries:
                    semantic_data = None
                    if entry.semantic_analysis:
                        semantic_data = {
                            'sentiment': entry.semantic_analysis.sentiment,
                            'urgency_score': entry.semantic_analysis.urgency_score,
                            'intent': entry.semantic_analysis.intent,
                            'equipment_mentioned': entry.semantic_analysis.equipment_mentioned,
                            'issues_identified': entry.semantic_analysis.issues_identified,
                            'recommended_actions': entry.semantic_analysis.recommended_actions,
                            'confidence_score': entry.semantic_analysis.confidence_score
                        }
                    
                    conn.execute(
                        self.calendar_table.insert().values(
                            entry_id=entry.entry_id,
                            description=entry.description,
                            date=entry.date,
                            category=entry.category,
                            client=entry.client,
                            address=entry.address,
                            city=entry.city,
                            phone=entry.phone,
                            device_type=entry.device_type,
                            brand=entry.brand,
                            model=entry.model,
                            quantity=entry.quantity,
                            notes=entry.notes,
                            ai_category=entry.ai_category,
                            ai_keywords=entry.ai_keywords,
                            ai_priority=entry.ai_priority,
                            semantic_analysis=semantic_data
                        )
                    )
                
                conn.commit()
            
            logger.info(f"Stored {len(entries)} calendar entries")
            
        except Exception as e:
            logger.error(f"Failed to store calendar entries: {e}")
            raise
    
    async def store_customer_profile(self, profile: CustomerProfile):
        """Store unified customer profile"""
        try:
            with self.db.postgres_engine.connect() as conn:
                conn.execute(
                    self.customer_profiles_table.insert().values(
                        customer_id=profile.customer_id,
                        name=profile.name,
                        phone_numbers=profile.phone_numbers,
                        email_addresses=profile.email_addresses,
                        addresses=profile.addresses,
                        service_history=profile.service_history,
                        equipment_registry=profile.equipment_registry,
                        semantic_insights=profile.semantic_insights,
                        customer_score=profile.customer_score
                    )
                )
                conn.commit()
            
            logger.info(f"Stored customer profile: {profile.customer_id}")
            
        except Exception as e:
            logger.error(f"Failed to store customer profile: {e}")
            raise


class EnhancedDataPipeline:
    """Main enhanced data pipeline orchestrator"""
    
    def __init__(self, config: DatabaseConfig, data_ingest_path: str):
        self.config = config
        self.data_ingest_path = data_ingest_path
        self.db_connections = DatabaseConnections(config)
        self.gobeklitepe_bridge = None
        self.csv_processor = None
        self.profile_aggregator = None
        self.db_manager = None
    
    async def initialize(self):
        """Initialize all pipeline components"""
        try:
            logger.info("Initializing Enhanced Data Pipeline...")
            
            # Initialize database connections
            await self.db_connections.connect_all()
            
            # Initialize Gobeklitepe bridge
            gobeklitepe_config = {
                'weaviate_url': os.getenv('WEAVIATE_URL', 'http://localhost:8080'),
                'openai_api_key': os.getenv('OPENAI_API_KEY'),
                'anthropic_api_key': os.getenv('ANTHROPIC_API_KEY')
            }
            
            self.gobeklitepe_bridge = GobeklitepeBridge(gobeklitepe_config)
            await self.gobeklitepe_bridge.initialize()
            
            # Initialize processors
            self.csv_processor = CSVDataProcessor(self.data_ingest_path, self.gobeklitepe_bridge)
            self.profile_aggregator = CustomerProfileAggregator(self.db_connections, self.gobeklitepe_bridge)
            self.db_manager = EnhancedDatabaseManager(self.db_connections)
            
            # Create enhanced tables
            await self.db_manager.create_tables()
            
            logger.info("Enhanced Data Pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Data Pipeline: {e}")
            raise
    
    async def run_full_pipeline(self):
        """Run the complete enhanced data pipeline"""
        try:
            logger.info("🚀 Starting Enhanced Data Pipeline with Semantic Integration...")
            
            # Phase 1: Process CSV data
            logger.info("📊 Phase 1: Processing CSV data...")
            calendar_entries = await self.csv_processor.process_calendar_data()
            customer_data = await self.csv_processor.process_customer_data()
            document_data = await self.csv_processor.process_document_data()
            
            # Phase 2: Store processed data
            logger.info("💾 Phase 2: Storing processed data...")
            if calendar_entries:
                await self.db_manager.store_calendar_entries(calendar_entries)
            
            # Phase 3: Create unified customer profiles
            logger.info("👥 Phase 3: Creating unified customer profiles...")
            unique_customers = set()
            
            # Extract unique customers from calendar data
            for entry in calendar_entries:
                if entry.client and entry.client.strip():
                    unique_customers.add(entry.client.strip())
            
            # Create profiles for each unique customer
            for customer_id in unique_customers:
                customer_entries = [e for e in calendar_entries if e.client == customer_id]
                profile = await self.profile_aggregator.create_unified_profile(
                    customer_identifier=customer_id,
                    calendar_entries=customer_entries
                )
                
                if profile:
                    await self.db_manager.store_customer_profile(profile)
            
            # Phase 4: Generate summary report
            logger.info("📈 Phase 4: Generating pipeline summary...")
            summary = {
                'calendar_entries_processed': len(calendar_entries),
                'customer_records_processed': len(customer_data),
                'document_records_processed': len(document_data),
                'unique_customers_identified': len(unique_customers),
                'semantic_analyses_performed': sum(1 for e in calendar_entries if e.semantic_analysis),
                'pipeline_completion_time': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"✅ Enhanced Data Pipeline completed successfully!")
            logger.info(f"📊 Summary: {json.dumps(summary, indent=2)}")
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Enhanced Data Pipeline failed: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup pipeline resources"""
        try:
            if self.db_connections:
                await self.db_connections.close_all()
            logger.info("Pipeline cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")


# Example usage and testing
async def main():
    """Main function to run the enhanced data pipeline"""
    
    # Configuration
    config = DatabaseConfig()
    data_ingest_path = "/home/<USER>/HVAC/unifikacja/Data_to_ingest"
    
    # Initialize and run pipeline
    pipeline = EnhancedDataPipeline(config, data_ingest_path)
    
    try:
        await pipeline.initialize()
        summary = await pipeline.run_full_pipeline()
        
        print("🌟 Enhanced Data Pipeline Summary:")
        print(json.dumps(summary, indent=2))
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        raise
    finally:
        await pipeline.cleanup()


if __name__ == "__main__":
    # Run the enhanced data pipeline
    asyncio.run(main())